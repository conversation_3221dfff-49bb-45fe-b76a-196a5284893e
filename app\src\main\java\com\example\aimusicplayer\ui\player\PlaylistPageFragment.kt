package com.example.aimusicplayer.ui.player

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.databinding.PagePlayerPlaylistBinding
import com.example.aimusicplayer.ui.adapter.PlayQueueAdapter
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 播放列表页面Fragment
 * 用于在ViewPager2中显示播放列表
 */
@AndroidEntryPoint
class PlaylistPageFragment : Fragment() {

    private val TAG = "PlaylistPageFragment"
    private var _binding: PagePlayerPlaylistBinding? = null
    private val binding get() = _binding!!

    private val playerViewModel: PlayerViewModel by activityViewModels()
    private lateinit var adapter: PlayQueueAdapter

    // 分页加载相关变量
    private var isLoadingMore = false
    private var hasMoreRecommendations = true
    private val loadMoreThreshold = 2 // 距离底部2个item时开始加载推荐

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PagePlayerPlaylistBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        setupObservers()
    }

    private fun setupViews() {
        // 设置返回按钮
        binding.btnBackToLyric?.setOnClickListener {
            // 返回到歌词页面
            (parentFragment as? PlayerFragment)?.switchToLyricPage()
        }

        // 设置RecyclerView
        setupRecyclerView()

        // 设置操作按钮
        setupActionButtons()
    }

    private fun setupRecyclerView() {
        adapter = PlayQueueAdapter(
            onItemClick = { position, mediaItem ->
                // 点击歌曲项，跳转播放
                playerViewModel.seekToQueueItem(position)
            },
            onItemLongClick = { position, mediaItem ->
                // 长按显示更多选项
                showItemOptions(position, mediaItem)
            }
        )

        binding.recyclerViewPlaylist.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            setHasFixedSize(true)
            // 车载优化：增加缓存大小
            setItemViewCacheSize(20)

            // 添加滚动监听器，实现自动加载更多推荐歌曲
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只在向下滚动时检查
                    if (dy > 0) {
                        checkAndLoadMoreRecommendations(recyclerView)
                    }
                }
            })
        }
    }

    private fun setupActionButtons() {
        // 播放模式切换按钮
        binding.layoutPlayMode?.setOnClickListener {
            switchPlayMode()
        }

        // 清空播放列表按钮
        binding.btnClearPlaylist?.setOnClickListener {
            showClearConfirmDialog()
        }

        // 随机播放按钮
        binding.btnShufflePlaylist?.setOnClickListener {
            playerViewModel.shufflePlaylist()
        }
    }

    private fun setupObservers() {
        // 观察播放队列
        viewLifecycleOwner.lifecycleScope.launch {
            playerViewModel.playQueueFlow.collect { queue ->
                adapter.submitList(queue)

                // 更新歌曲数量
                binding.textSongCount?.text = "(${queue.size}首)"

                // 显示/隐藏空状态
                if (queue.isEmpty()) {
                    binding.recyclerViewPlaylist.visibility = View.GONE
                    binding.layoutEmptyState?.visibility = View.VISIBLE
                } else {
                    binding.recyclerViewPlaylist.visibility = View.VISIBLE
                    binding.layoutEmptyState?.visibility = View.GONE
                }
            }
        }

        // 观察当前播放索引
        viewLifecycleOwner.lifecycleScope.launch {
            playerViewModel.currentMediaItemIndex.collect { index ->
                adapter.setCurrentPlayingIndex(index)

                // 滚动到当前播放的歌曲
                if (index >= 0) {
                    binding.recyclerViewPlaylist.scrollToPosition(index)
                }
            }
        }

        // 观察播放模式
        viewLifecycleOwner.lifecycleScope.launch {
            playerViewModel.repeatMode.collect { mode ->
                updatePlayModeUI(mode)
            }
        }
    }

    private fun updatePlayModeUI(repeatMode: Int) {
        when (repeatMode) {
            0 -> { // REPEAT_MODE_OFF
                binding.imagePlayMode?.setImageResource(com.example.aimusicplayer.R.drawable.ic_repeat_off)
                binding.textPlayMode?.text = "顺序播放"
            }
            1 -> { // REPEAT_MODE_ONE
                binding.imagePlayMode?.setImageResource(com.example.aimusicplayer.R.drawable.ic_repeat_one)
                binding.textPlayMode?.text = "单曲循环"
            }
            2 -> { // REPEAT_MODE_ALL
                binding.imagePlayMode?.setImageResource(com.example.aimusicplayer.R.drawable.ic_repeat)
                binding.textPlayMode?.text = "列表循环"
            }
        }
    }

    private fun switchPlayMode() {
        playerViewModel.toggleRepeatMode()
    }

    private fun showItemOptions(position: Int, mediaItem: androidx.media3.common.MediaItem) {
        val options = arrayOf("从队列中移除", "查看歌曲信息")

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(mediaItem.mediaMetadata.title)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> removeFromQueue(position)
                    1 -> showSongInfo(mediaItem)
                }
            }
            .show()
    }

    private fun removeFromQueue(position: Int) {
        playerViewModel.removeFromQueue(position)
    }

    private fun showSongInfo(mediaItem: androidx.media3.common.MediaItem) {
        val title = mediaItem.mediaMetadata.title ?: "未知歌曲"
        val artist = mediaItem.mediaMetadata.artist ?: "未知艺术家"
        val duration = mediaItem.mediaMetadata.extras?.getLong("duration") ?: 0L

        val message = """
            歌曲：$title
            艺术家：$artist
            时长：${formatDuration(duration)}
            ID：${mediaItem.mediaId}
        """.trimIndent()

        MaterialAlertDialogBuilder(requireContext())
            .setTitle("歌曲信息")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show()
    }

    private fun showClearConfirmDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("清空播放列表")
            .setMessage("确定要清空当前播放列表吗？")
            .setPositiveButton("确定") { _, _ ->
                playerViewModel.clearQueue()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun formatDuration(duration: Long): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%02d:%02d", minutes, seconds)
    }

    /**
     * 检查并加载更多推荐歌曲
     */
    private fun checkAndLoadMoreRecommendations(recyclerView: RecyclerView) {
        if (isLoadingMore || !hasMoreRecommendations) {
            return
        }

        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
        val visibleItemCount = layoutManager.childCount
        val totalItemCount = layoutManager.itemCount
        val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

        // 当滚动到距离底部loadMoreThreshold个item时，开始加载更多推荐
        if ((visibleItemCount + firstVisibleItemPosition) >= (totalItemCount - loadMoreThreshold)) {
            loadMoreRecommendations()
        }
    }

    /**
     * 加载更多推荐歌曲
     */
    private fun loadMoreRecommendations() {
        if (isLoadingMore || !hasMoreRecommendations) {
            return
        }

        isLoadingMore = true
        Log.d(TAG, "开始加载更多推荐歌曲")

        // 获取当前播放的歌曲，用于推荐相似歌曲
        val currentSong = playerViewModel.currentSong.value
        if (currentSong != null) {
            // 这里可以调用ViewModel的方法来加载推荐歌曲
            // 例如：playerViewModel.loadRecommendations(currentSong.id)

            // 模拟加载推荐歌曲（实际项目中应该调用API）
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                isLoadingMore = false

                // 模拟加载了一些推荐歌曲
                // 实际实现中，这里应该是从API获取推荐歌曲并添加到播放队列
                Log.d(TAG, "推荐歌曲加载完成")

                // 如果没有更多推荐歌曲，设置hasMoreRecommendations = false
                // hasMoreRecommendations = false
            }, 1000)
        } else {
            isLoadingMore = false
            hasMoreRecommendations = false
            Log.d(TAG, "当前没有播放歌曲，无法加载推荐")
        }
    }

    /**
     * 重置推荐加载状态
     */
    private fun resetRecommendationState() {
        isLoadingMore = false
        hasMoreRecommendations = true
        Log.d(TAG, "重置推荐加载状态")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
