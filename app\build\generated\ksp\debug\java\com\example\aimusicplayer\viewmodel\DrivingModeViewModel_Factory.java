package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.MusicRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DrivingModeViewModel_Factory implements Factory<DrivingModeViewModel> {
  private final Provider<MusicRepository> musicRepositoryProvider;

  public DrivingModeViewModel_Factory(Provider<MusicRepository> musicRepositoryProvider) {
    this.musicRepositoryProvider = musicRepositoryProvider;
  }

  @Override
  public DrivingModeViewModel get() {
    return newInstance(musicRepositoryProvider.get());
  }

  public static DrivingModeViewModel_Factory create(
      Provider<MusicRepository> musicRepositoryProvider) {
    return new DrivingModeViewModel_Factory(musicRepositoryProvider);
  }

  public static DrivingModeViewModel newInstance(MusicRepository musicRepository) {
    return new DrivingModeViewModel(musicRepository);
  }
}
