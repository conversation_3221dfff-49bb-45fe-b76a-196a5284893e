package com.example.aimusicplayer.ui.player;

import com.example.aimusicplayer.network.NetworkStateManager;
import com.example.aimusicplayer.service.UserService;
import com.example.aimusicplayer.utils.AlbumArtCache;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerFragment_MembersInjector implements MembersInjector<PlayerFragment> {
  private final Provider<AlbumArtCache> albumArtCacheProvider;

  private final Provider<NetworkStateManager> networkStateManagerProvider;

  private final Provider<UserService> userServiceProvider;

  public PlayerFragment_MembersInjector(Provider<AlbumArtCache> albumArtCacheProvider,
      Provider<NetworkStateManager> networkStateManagerProvider,
      Provider<UserService> userServiceProvider) {
    this.albumArtCacheProvider = albumArtCacheProvider;
    this.networkStateManagerProvider = networkStateManagerProvider;
    this.userServiceProvider = userServiceProvider;
  }

  public static MembersInjector<PlayerFragment> create(
      Provider<AlbumArtCache> albumArtCacheProvider,
      Provider<NetworkStateManager> networkStateManagerProvider,
      Provider<UserService> userServiceProvider) {
    return new PlayerFragment_MembersInjector(albumArtCacheProvider, networkStateManagerProvider, userServiceProvider);
  }

  @Override
  public void injectMembers(PlayerFragment instance) {
    injectAlbumArtCache(instance, albumArtCacheProvider.get());
    injectNetworkStateManager(instance, networkStateManagerProvider.get());
    injectUserService(instance, userServiceProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.PlayerFragment.albumArtCache")
  public static void injectAlbumArtCache(PlayerFragment instance, AlbumArtCache albumArtCache) {
    instance.albumArtCache = albumArtCache;
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.PlayerFragment.networkStateManager")
  public static void injectNetworkStateManager(PlayerFragment instance,
      NetworkStateManager networkStateManager) {
    instance.networkStateManager = networkStateManager;
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.PlayerFragment.userService")
  public static void injectUserService(PlayerFragment instance, UserService userService) {
    instance.userService = userService;
  }
}
