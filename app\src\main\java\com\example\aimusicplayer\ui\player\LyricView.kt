package com.example.aimusicplayer.ui.player

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Typeface
import android.text.TextPaint
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.LyricLine
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 歌词视图
 * 用于显示歌词并支持滚动
 */
class LyricView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "LyricView"

        // 默认行数
        private const val DEFAULT_LINE_COUNT = 5

        // 默认行高
        private const val DEFAULT_LINE_HEIGHT = 80f

        // 默认字体大小
        private const val DEFAULT_TEXT_SIZE = 40f

        // 默认高亮字体大小
        private const val DEFAULT_HIGHLIGHT_TEXT_SIZE = 45f

        // 默认动画持续时间
        private const val DEFAULT_ANIMATION_DURATION = 300L

        // 默认无歌词文本
        private const val DEFAULT_NO_LYRIC_TEXT = "暂无歌词"

        // 拖动阈值
        private const val DRAG_THRESHOLD = 10
    }

    // 歌词行列表
    private var lyricLines: List<LyricLine> = emptyList()

    // 当前行索引
    private var currentLineIndex = 0

    // 自定义无歌词文本
    private var customNoLyricText: String? = null

    // 行高
    private var lineHeight = DEFAULT_LINE_HEIGHT

    // 行数
    private var lineCount = DEFAULT_LINE_COUNT

    // 普通文本画笔
    private val normalTextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.white)
        alpha = 160
        textSize = DEFAULT_TEXT_SIZE
        textAlign = Paint.Align.CENTER
    }

    // 高亮文本画笔
    private val highlightTextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.white)
        textSize = DEFAULT_HIGHLIGHT_TEXT_SIZE
        textAlign = Paint.Align.CENTER
        typeface = Typeface.DEFAULT_BOLD
    }

    // 翻译文本画笔
    private val translationTextPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.white)
        alpha = 128
        textSize = DEFAULT_TEXT_SIZE * 0.8f
        textAlign = Paint.Align.CENTER
    }

    // 偏移量
    private var offset = 0f

    // 目标偏移量
    private var targetOffset = 0f

    // 滚动动画
    private var scrollAnimator: ValueAnimator? = null

    // 是否正在拖动
    private var isDragging = false

    // 拖动开始Y坐标
    private var dragStartY = 0f

    // 拖动开始偏移量
    private var dragStartOffset = 0f



    // 是否显示翻译
    private var showTranslation = true

    // 手势检测器 - 优化响应时间
    private val gestureDetector = GestureDetector(
        context,
        object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                // 单击事件 - 优化响应时间<300ms
                val startTime = System.currentTimeMillis()

                if (!isDragging && lyricLines.isNotEmpty()) {
                    // 计算点击位置对应的行索引
                    val centerY = height / 2f
                    val touchY = e.y
                    val touchOffset = touchY - centerY
                    val touchLineIndex = currentLineIndex + (touchOffset / lineHeight).toInt()

                    // 检查索引是否有效
                    if (touchLineIndex in lyricLines.indices) {
                        // 立即提供视觉反馈
                        performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY)

                        // 通知监听器
                        onLyricClickListener?.invoke(lyricLines[touchLineIndex])

                        // 监控响应时间
                        val responseTime = System.currentTimeMillis() - startTime
                        Log.d(TAG, "歌词点击响应时间: ${responseTime}ms")
                    }
                }
                return true
            }
        },
    )

    // 歌词点击监听器
    var onLyricClickListener: ((LyricLine) -> Unit)? = null

    // 拖动状态变化监听器
    var onDragStateChangeListener: ((Boolean) -> Unit)? = null

    // 拖动位置变化监听器
    var onDragPositionChangeListener: ((LyricLine) -> Unit)? = null

    /**
     * 设置歌词
     * @param lyrics 歌词行列表
     */
    fun setLyrics(lyrics: List<LyricLine>) {
        lyricLines = lyrics
        currentLineIndex = 0
        offset = 0f
        targetOffset = 0f
        customNoLyricText = null // 重置自定义文本
        invalidate()
    }

    /**
     * 设置自定义无歌词文本（用于轻音乐等特殊情况）
     * @param text 自定义文本
     */
    fun setCustomNoLyricText(text: String?) {
        customNoLyricText = text
        if (lyricLines.isEmpty()) {
            invalidate()
        }
    }

    /**
     * 更新当前行 - 性能优化版本，提高同步精度
     * @param position 当前播放位置（毫秒）
     * @param smooth 是否平滑滚动
     */
    fun updateCurrentLine(position: Long, smooth: Boolean = true) {
        if (lyricLines.isEmpty()) return

        // 优化：使用二分查找提高性能
        val index = findCurrentLineIndex(position)

        // 提高同步精度：即使是同一行，也检查是否需要微调
        if (index != currentLineIndex || shouldUpdateForPrecision(position)) {
            currentLineIndex = index
            updateOffset(smooth)
        }
    }

    /**
     * 检查是否需要为精度而更新
     * @param position 当前播放位置（毫秒）
     * @return 是否需要更新
     */
    private fun shouldUpdateForPrecision(position: Long): Boolean {
        if (currentLineIndex >= lyricLines.size) return false

        val currentLine = lyricLines[currentLineIndex]
        val nextLine = if (currentLineIndex + 1 < lyricLines.size) lyricLines[currentLineIndex + 1] else null

        // 如果当前行时间与播放位置差距超过100ms，需要更新以保持精度
        return abs(currentLine.time - position) > 100 ||
            (nextLine != null && abs(nextLine.time - position) < abs(currentLine.time - position))
    }

    /**
     * 使用二分查找快速定位当前歌词行
     * @param position 当前播放位置（毫秒）
     * @return 当前行索引
     */
    private fun findCurrentLineIndex(position: Long): Int {
        if (lyricLines.isEmpty()) return 0

        var left = 0
        var right = lyricLines.size - 1

        while (left <= right) {
            val mid = (left + right) / 2
            val midTime = lyricLines[mid].time

            when {
                midTime == position -> return mid
                midTime < position -> {
                    // 检查是否是最后一行或下一行时间大于position
                    if (mid == lyricLines.size - 1 || lyricLines[mid + 1].time > position) {
                        return mid
                    }
                    left = mid + 1
                }
                else -> right = mid - 1
            }
        }

        return max(0, right)
    }

    /**
     * 更新偏移量 - 优化动画性能
     * @param smooth 是否平滑滚动
     */
    private fun updateOffset(smooth: Boolean = true) {
        // 计算目标偏移量
        targetOffset = -currentLineIndex * lineHeight

        if (smooth) {
            // 优化：如果偏移量变化很小，直接设置而不使用动画
            val offsetDiff = abs(targetOffset - offset)
            if (offsetDiff < lineHeight * 0.1f) {
                offset = targetOffset
                invalidate()
                return
            }

            // 创建优化的滚动动画
            scrollAnimator?.cancel()
            scrollAnimator = ValueAnimator.ofFloat(offset, targetOffset).apply {
                // 根据距离调整动画时长，提高流畅度
                duration = min(DEFAULT_ANIMATION_DURATION, (offsetDiff / lineHeight * 150).toLong())
                interpolator = android.view.animation.DecelerateInterpolator(1.5f)
                addUpdateListener { animation ->
                    offset = animation.animatedValue as Float
                    // 优化：减少不必要的重绘
                    if (isShown) {
                        invalidate()
                    }
                }
                start()
            }
        } else {
            // 直接设置偏移量
            offset = targetOffset
            invalidate()
        }
    }

    /**
     * 设置是否显示翻译
     * @param show 是否显示
     */
    fun setShowTranslation(show: Boolean) {
        showTranslation = show
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        val centerY = height / 2f

        if (lyricLines.isEmpty()) {
            // 绘制无歌词提示，优先使用自定义文本
            val noLyricText = customNoLyricText ?: DEFAULT_NO_LYRIC_TEXT
            canvas.drawText(noLyricText, centerX, centerY, highlightTextPaint)
            return
        }

        // 优化：计算可见行范围，减少绘制操作
        val visibleLineCount = (height / lineHeight).toInt() + 2 // 额外绘制2行确保平滑
        val startLine = max(0, currentLineIndex - visibleLineCount / 2)
        val endLine = min(lyricLines.size - 1, currentLineIndex + visibleLineCount / 2)

        // 绘制歌词行 - 性能优化版本
        for (i in startLine..endLine) {
            val line = lyricLines[i]
            val y = centerY + (i - currentLineIndex) * lineHeight + offset

            // 优化：更精确的可见性检查
            if (y < -lineHeight * 0.5f || y > height + lineHeight * 0.5f) {
                continue
            }

            // 选择画笔
            val paint = if (i == currentLineIndex) highlightTextPaint else normalTextPaint

            // 绘制歌词文本
            canvas.drawText(line.text, centerX, y, paint)

            // 绘制翻译文本 - 优化：减少条件判断
            if (showTranslation && line.translation?.isNotEmpty() == true) {
                canvas.drawText(line.translation, centerX, y + lineHeight * 0.5f, translationTextPaint)
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 先交给手势检测器处理
        if (gestureDetector.onTouchEvent(event)) {
            return true
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 取消滚动动画
                scrollAnimator?.cancel()

                // 记录拖动开始位置
                dragStartY = event.y
                dragStartOffset = offset

                return true
            }
            MotionEvent.ACTION_MOVE -> {
                val deltaY = event.y - dragStartY

                // 检查是否达到拖动阈值
                if (!isDragging && abs(deltaY) > DRAG_THRESHOLD) {
                    isDragging = true
                    onDragStateChangeListener?.invoke(true)
                }

                if (isDragging) {
                    // 更新偏移量
                    offset = dragStartOffset + deltaY

                    // 计算当前拖动位置对应的行索引
                    val dragLineIndex = -offset / lineHeight
                    val newLineIndex = dragLineIndex.toInt().coerceIn(0, lyricLines.size - 1)

                    // 如果行索引变化，通知监听器
                    if (newLineIndex != currentLineIndex) {
                        currentLineIndex = newLineIndex
                        onDragPositionChangeListener?.invoke(lyricLines[currentLineIndex])
                    }

                    invalidate()
                    return true
                }

                return false
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    // 结束拖动
                    isDragging = false
                    onDragStateChangeListener?.invoke(false)

                    // 更新偏移量
                    updateOffset()
                    return true
                }

                return false
            }
        }

        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 根据视图高度调整行高和行数
        lineHeight = h / (lineCount * 2 + 1).toFloat()

        // 更新偏移量
        updateOffset(false)
    }
}
