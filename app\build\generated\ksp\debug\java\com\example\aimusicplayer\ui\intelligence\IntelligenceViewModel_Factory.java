package com.example.aimusicplayer.ui.intelligence;

import com.example.aimusicplayer.data.source.MusicDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class IntelligenceViewModel_Factory implements Factory<IntelligenceViewModel> {
  private final Provider<MusicDataSource> musicDataSourceProvider;

  public IntelligenceViewModel_Factory(Provider<MusicDataSource> musicDataSourceProvider) {
    this.musicDataSourceProvider = musicDataSourceProvider;
  }

  @Override
  public IntelligenceViewModel get() {
    return newInstance(musicDataSourceProvider.get());
  }

  public static IntelligenceViewModel_Factory create(
      Provider<MusicDataSource> musicDataSourceProvider) {
    return new IntelligenceViewModel_Factory(musicDataSourceProvider);
  }

  public static IntelligenceViewModel newInstance(MusicDataSource musicDataSource) {
    return new IntelligenceViewModel(musicDataSource);
  }
}
