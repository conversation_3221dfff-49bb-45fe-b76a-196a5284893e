package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SplashViewModel_Factory implements Factory<SplashViewModel> {
  private final Provider<UserRepository> userRepositoryProvider;

  public SplashViewModel_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public SplashViewModel get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static SplashViewModel_Factory create(Provider<UserRepository> userRepositoryProvider) {
    return new SplashViewModel_Factory(userRepositoryProvider);
  }

  public static SplashViewModel newInstance(UserRepository userRepository) {
    return new SplashViewModel(userRepository);
  }
}
