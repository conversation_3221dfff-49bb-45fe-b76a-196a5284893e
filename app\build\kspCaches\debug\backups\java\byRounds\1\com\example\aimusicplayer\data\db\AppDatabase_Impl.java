package com.example.aimusicplayer.data.db;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.aimusicplayer.data.db.dao.ApiCacheDao;
import com.example.aimusicplayer.data.db.dao.ApiCacheDao_Impl;
import com.example.aimusicplayer.data.db.dao.PlaylistDao;
import com.example.aimusicplayer.data.db.dao.PlaylistDao_Impl;
import com.example.aimusicplayer.data.db.dao.SongDao;
import com.example.aimusicplayer.data.db.dao.SongDao_Impl;
import com.example.aimusicplayer.data.db.dao.UserDao;
import com.example.aimusicplayer.data.db.dao.UserDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile SongDao _songDao;

  private volatile PlaylistDao _playlistDao;

  private volatile UserDao _userDao;

  private volatile ApiCacheDao _apiCacheDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(3) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `songs` (`type` INTEGER NOT NULL, `song_id` INTEGER NOT NULL, `title` TEXT NOT NULL, `artist` TEXT NOT NULL, `artist_id` INTEGER NOT NULL, `album` TEXT NOT NULL, `album_id` INTEGER NOT NULL, `album_cover` TEXT NOT NULL, `duration` INTEGER NOT NULL, `uri` TEXT NOT NULL DEFAULT '', `path` TEXT NOT NULL, `file_name` TEXT NOT NULL, `file_size` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `is_favorite` INTEGER NOT NULL, `last_played_time` INTEGER NOT NULL, `unique_id` TEXT NOT NULL, PRIMARY KEY(`unique_id`))");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_songs_title` ON `songs` (`title`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_songs_artist` ON `songs` (`artist`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_songs_album` ON `songs` (`album`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `playlists` (`playlist_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `cover_url` TEXT NOT NULL, `description` TEXT NOT NULL, `creator_id` INTEGER NOT NULL, `creator_name` TEXT NOT NULL, `song_count` INTEGER NOT NULL, `play_count` INTEGER NOT NULL, `is_subscribed` INTEGER NOT NULL, `create_time` INTEGER NOT NULL, `update_time` INTEGER NOT NULL, `is_local` INTEGER NOT NULL, PRIMARY KEY(`playlist_id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `playlist_song_cross_ref` (`playlist_id` INTEGER NOT NULL, `song_unique_id` TEXT NOT NULL, `sort_order` INTEGER NOT NULL, `add_time` INTEGER NOT NULL, PRIMARY KEY(`playlist_id`, `song_unique_id`))");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_playlist_song_cross_ref_playlist_id` ON `playlist_song_cross_ref` (`playlist_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_playlist_song_cross_ref_song_unique_id` ON `playlist_song_cross_ref` (`song_unique_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `users` (`user_id` INTEGER NOT NULL, `username` TEXT NOT NULL, `nickname` TEXT NOT NULL, `avatar_url` TEXT NOT NULL, `background_url` TEXT NOT NULL, `signature` TEXT NOT NULL, `follows` INTEGER NOT NULL, `followers` INTEGER NOT NULL, `level` INTEGER NOT NULL, `vip_type` INTEGER NOT NULL, `last_login_time` INTEGER NOT NULL, `cookie` TEXT NOT NULL, PRIMARY KEY(`user_id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `api_cache` (`cache_key` TEXT NOT NULL, `data` TEXT NOT NULL, `cache_time` INTEGER NOT NULL, `expiration_time` INTEGER NOT NULL, `cache_type` TEXT NOT NULL, PRIMARY KEY(`cache_key`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e090f777145fb91f3b50ad2734377926')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `songs`");
        db.execSQL("DROP TABLE IF EXISTS `playlists`");
        db.execSQL("DROP TABLE IF EXISTS `playlist_song_cross_ref`");
        db.execSQL("DROP TABLE IF EXISTS `users`");
        db.execSQL("DROP TABLE IF EXISTS `api_cache`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsSongs = new HashMap<String, TableInfo.Column>(17);
        _columnsSongs.put("type", new TableInfo.Column("type", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("song_id", new TableInfo.Column("song_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("artist", new TableInfo.Column("artist", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("artist_id", new TableInfo.Column("artist_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("album", new TableInfo.Column("album", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("album_id", new TableInfo.Column("album_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("album_cover", new TableInfo.Column("album_cover", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("duration", new TableInfo.Column("duration", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("uri", new TableInfo.Column("uri", "TEXT", true, 0, "''", TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("path", new TableInfo.Column("path", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("file_name", new TableInfo.Column("file_name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("file_size", new TableInfo.Column("file_size", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("is_vip", new TableInfo.Column("is_vip", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("is_favorite", new TableInfo.Column("is_favorite", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("last_played_time", new TableInfo.Column("last_played_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSongs.put("unique_id", new TableInfo.Column("unique_id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSongs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSongs = new HashSet<TableInfo.Index>(3);
        _indicesSongs.add(new TableInfo.Index("index_songs_title", false, Arrays.asList("title"), Arrays.asList("ASC")));
        _indicesSongs.add(new TableInfo.Index("index_songs_artist", false, Arrays.asList("artist"), Arrays.asList("ASC")));
        _indicesSongs.add(new TableInfo.Index("index_songs_album", false, Arrays.asList("album"), Arrays.asList("ASC")));
        final TableInfo _infoSongs = new TableInfo("songs", _columnsSongs, _foreignKeysSongs, _indicesSongs);
        final TableInfo _existingSongs = TableInfo.read(db, "songs");
        if (!_infoSongs.equals(_existingSongs)) {
          return new RoomOpenHelper.ValidationResult(false, "songs(com.example.aimusicplayer.data.db.entity.SongEntity).\n"
                  + " Expected:\n" + _infoSongs + "\n"
                  + " Found:\n" + _existingSongs);
        }
        final HashMap<String, TableInfo.Column> _columnsPlaylists = new HashMap<String, TableInfo.Column>(12);
        _columnsPlaylists.put("playlist_id", new TableInfo.Column("playlist_id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("cover_url", new TableInfo.Column("cover_url", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("creator_id", new TableInfo.Column("creator_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("creator_name", new TableInfo.Column("creator_name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("song_count", new TableInfo.Column("song_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("play_count", new TableInfo.Column("play_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("is_subscribed", new TableInfo.Column("is_subscribed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("create_time", new TableInfo.Column("create_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("update_time", new TableInfo.Column("update_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylists.put("is_local", new TableInfo.Column("is_local", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysPlaylists = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesPlaylists = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoPlaylists = new TableInfo("playlists", _columnsPlaylists, _foreignKeysPlaylists, _indicesPlaylists);
        final TableInfo _existingPlaylists = TableInfo.read(db, "playlists");
        if (!_infoPlaylists.equals(_existingPlaylists)) {
          return new RoomOpenHelper.ValidationResult(false, "playlists(com.example.aimusicplayer.data.db.entity.PlaylistEntity).\n"
                  + " Expected:\n" + _infoPlaylists + "\n"
                  + " Found:\n" + _existingPlaylists);
        }
        final HashMap<String, TableInfo.Column> _columnsPlaylistSongCrossRef = new HashMap<String, TableInfo.Column>(4);
        _columnsPlaylistSongCrossRef.put("playlist_id", new TableInfo.Column("playlist_id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylistSongCrossRef.put("song_unique_id", new TableInfo.Column("song_unique_id", "TEXT", true, 2, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylistSongCrossRef.put("sort_order", new TableInfo.Column("sort_order", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPlaylistSongCrossRef.put("add_time", new TableInfo.Column("add_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysPlaylistSongCrossRef = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesPlaylistSongCrossRef = new HashSet<TableInfo.Index>(2);
        _indicesPlaylistSongCrossRef.add(new TableInfo.Index("index_playlist_song_cross_ref_playlist_id", false, Arrays.asList("playlist_id"), Arrays.asList("ASC")));
        _indicesPlaylistSongCrossRef.add(new TableInfo.Index("index_playlist_song_cross_ref_song_unique_id", false, Arrays.asList("song_unique_id"), Arrays.asList("ASC")));
        final TableInfo _infoPlaylistSongCrossRef = new TableInfo("playlist_song_cross_ref", _columnsPlaylistSongCrossRef, _foreignKeysPlaylistSongCrossRef, _indicesPlaylistSongCrossRef);
        final TableInfo _existingPlaylistSongCrossRef = TableInfo.read(db, "playlist_song_cross_ref");
        if (!_infoPlaylistSongCrossRef.equals(_existingPlaylistSongCrossRef)) {
          return new RoomOpenHelper.ValidationResult(false, "playlist_song_cross_ref(com.example.aimusicplayer.data.db.entity.PlaylistSongCrossRef).\n"
                  + " Expected:\n" + _infoPlaylistSongCrossRef + "\n"
                  + " Found:\n" + _existingPlaylistSongCrossRef);
        }
        final HashMap<String, TableInfo.Column> _columnsUsers = new HashMap<String, TableInfo.Column>(12);
        _columnsUsers.put("user_id", new TableInfo.Column("user_id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("username", new TableInfo.Column("username", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("nickname", new TableInfo.Column("nickname", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("avatar_url", new TableInfo.Column("avatar_url", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("background_url", new TableInfo.Column("background_url", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("signature", new TableInfo.Column("signature", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("follows", new TableInfo.Column("follows", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("followers", new TableInfo.Column("followers", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("level", new TableInfo.Column("level", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("vip_type", new TableInfo.Column("vip_type", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("last_login_time", new TableInfo.Column("last_login_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUsers.put("cookie", new TableInfo.Column("cookie", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUsers = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUsers = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUsers = new TableInfo("users", _columnsUsers, _foreignKeysUsers, _indicesUsers);
        final TableInfo _existingUsers = TableInfo.read(db, "users");
        if (!_infoUsers.equals(_existingUsers)) {
          return new RoomOpenHelper.ValidationResult(false, "users(com.example.aimusicplayer.data.db.entity.UserEntity).\n"
                  + " Expected:\n" + _infoUsers + "\n"
                  + " Found:\n" + _existingUsers);
        }
        final HashMap<String, TableInfo.Column> _columnsApiCache = new HashMap<String, TableInfo.Column>(5);
        _columnsApiCache.put("cache_key", new TableInfo.Column("cache_key", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsApiCache.put("data", new TableInfo.Column("data", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsApiCache.put("cache_time", new TableInfo.Column("cache_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsApiCache.put("expiration_time", new TableInfo.Column("expiration_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsApiCache.put("cache_type", new TableInfo.Column("cache_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysApiCache = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesApiCache = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoApiCache = new TableInfo("api_cache", _columnsApiCache, _foreignKeysApiCache, _indicesApiCache);
        final TableInfo _existingApiCache = TableInfo.read(db, "api_cache");
        if (!_infoApiCache.equals(_existingApiCache)) {
          return new RoomOpenHelper.ValidationResult(false, "api_cache(com.example.aimusicplayer.data.db.entity.ApiCacheEntity).\n"
                  + " Expected:\n" + _infoApiCache + "\n"
                  + " Found:\n" + _existingApiCache);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "e090f777145fb91f3b50ad2734377926", "c65ede7b1da549babe3967ea6b06d7ec");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "songs","playlists","playlist_song_cross_ref","users","api_cache");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `songs`");
      _db.execSQL("DELETE FROM `playlists`");
      _db.execSQL("DELETE FROM `playlist_song_cross_ref`");
      _db.execSQL("DELETE FROM `users`");
      _db.execSQL("DELETE FROM `api_cache`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(SongDao.class, SongDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(PlaylistDao.class, PlaylistDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserDao.class, UserDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ApiCacheDao.class, ApiCacheDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public SongDao songDao() {
    if (_songDao != null) {
      return _songDao;
    } else {
      synchronized(this) {
        if(_songDao == null) {
          _songDao = new SongDao_Impl(this);
        }
        return _songDao;
      }
    }
  }

  @Override
  public PlaylistDao playlistDao() {
    if (_playlistDao != null) {
      return _playlistDao;
    } else {
      synchronized(this) {
        if(_playlistDao == null) {
          _playlistDao = new PlaylistDao_Impl(this);
        }
        return _playlistDao;
      }
    }
  }

  @Override
  public UserDao userDao() {
    if (_userDao != null) {
      return _userDao;
    } else {
      synchronized(this) {
        if(_userDao == null) {
          _userDao = new UserDao_Impl(this);
        }
        return _userDao;
      }
    }
  }

  @Override
  public ApiCacheDao apiCacheDao() {
    if (_apiCacheDao != null) {
      return _apiCacheDao;
    } else {
      synchronized(this) {
        if(_apiCacheDao == null) {
          _apiCacheDao = new ApiCacheDao_Impl(this);
        }
        return _apiCacheDao;
      }
    }
  }
}
