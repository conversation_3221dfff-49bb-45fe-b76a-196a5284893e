package com.example.aimusicplayer.viewmodel

import android.net.Uri
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.utils.Constants
import com.example.aimusicplayer.utils.NetworkResult
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 播放器ViewModel - 严格按照ponymusic项目标准实现
 *
 * 负责管理音乐播放器的所有状态和业务逻辑，包括：
 * - 播放状态管理（播放/暂停/停止）
 * - 播放列表管理（添加/删除/排序）
 * - 歌词显示和同步
 * - 收藏功能
 * - 评论功能
 *
 * 架构特点：
 * - 使用StateFlow进行状态管理，确保UI响应性
 * - 采用kotlin.runCatching错误处理模式
 * - 严格遵循MVVM架构，不持有View引用
 * - 使用Hilt依赖注入，便于测试和维护
 *
 * 性能要求：
 * - 播放列表操作 < 100ms
 * - 歌词解析 < 200ms
 * - 收藏状态检查 < 50ms
 * - UI响应时间 < 200ms
 *
 * <AUTHOR> Music Player Team
 * @since 1.0.0
 */
@HiltViewModel
class PlayerViewModel @Inject constructor(
    private val musicRepository: MusicRepository,
    private val musicFileCache: com.example.aimusicplayer.data.cache.MusicFileCache,
) : ViewModel() {

    // 严格按照ponymusic项目标准的StateFlow使用
    private val _currentSongFlow = MutableStateFlow<Song?>(null)
    val currentSongFlow = _currentSongFlow.toUnMutable()

    private val _isPlayingFlow = MutableStateFlow(false)
    val isPlayingFlow = _isPlayingFlow.toUnMutable()

    private val _playQueueFlow = MutableStateFlow<List<MediaItem>>(emptyList())
    val playQueueFlow = _playQueueFlow.toUnMutable()

    private val _commentsFlow = MutableStateFlow<List<Comment>>(emptyList())
    val commentsFlow = _commentsFlow.toUnMutable()

    private val _hotCommentsFlow = MutableStateFlow<List<Comment>>(emptyList())
    val hotCommentsFlow = _hotCommentsFlow.toUnMutable()

    private val _commentCountFlow = MutableStateFlow(0)
    val commentCountFlow = _commentCountFlow.toUnMutable()

    private val _isLikedFlow = MutableStateFlow(false)
    val isLikedFlow = _isLikedFlow.toUnMutable()

    private val _isCurrentSongCollected = MutableStateFlow(false)
    val isCurrentSongCollected = _isCurrentSongCollected.toUnMutable()

    private val _similarSongsFlow = MutableStateFlow<List<Song>>(emptyList())
    val similarSongsFlow = _similarSongsFlow.toUnMutable()

    // 播放状态相关
    private val _playState = MutableStateFlow(PlayState.IDLE)
    val playState = _playState.toUnMutable()

    // 播放进度相关
    private val _playProgress = MutableStateFlow(PlayProgress(0L, 0L))
    val playProgress = _playProgress.toUnMutable()

    // 播放模式相关
    private val _playMode = MutableStateFlow(PlayMode.LOOP)
    val playMode = _playMode.toUnMutable()

    // 心动模式状态
    private val _heartModeEnabled = MutableStateFlow(false)
    val heartModeEnabled = _heartModeEnabled.toUnMutable()

    // 心动模式推荐歌曲
    private val _heartModeRecommendations = MutableStateFlow<List<Song>>(emptyList())
    val heartModeRecommendations = _heartModeRecommendations.toUnMutable()

    // 歌词相关
    private val _lyrics = MutableStateFlow<List<LyricLine>>(emptyList())
    val lyrics = _lyrics.toUnMutable()

    // 搜索相关
    private val _searchItems = MutableStateFlow<List<String>>(emptyList())
    val searchItems = _searchItems.toUnMutable()

    private val _searchResults = MutableStateFlow<List<Song>>(emptyList())
    val searchResults = _searchResults.toUnMutable()

    private val _isSearching = MutableStateFlow(false)
    val isSearching = _isSearching.toUnMutable()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage = _errorMessage.toUnMutable()

    // UI兼容属性
    val currentSong = currentSongFlow
    val currentPosition = MutableStateFlow(0L).toUnMutable()
    val duration = MutableStateFlow(0L).toUnMutable()

    // 播放队列相关
    private val _currentMediaItemIndex = MutableStateFlow(-1)
    val currentMediaItemIndex = _currentMediaItemIndex.toUnMutable()

    private val _repeatMode = MutableStateFlow(0) // 0: OFF, 1: ONE, 2: ALL
    val repeatMode = _repeatMode.toUnMutable()

    companion object {
        private const val TAG = "PlayerViewModel"
    }

    // 播放状态枚举
    enum class PlayState {
        IDLE, PLAYING, PAUSED, BUFFERING, ERROR
    }

    // 播放模式枚举
    enum class PlayMode {
        LOOP, SHUFFLE, SINGLE
    }

    // 播放进度数据类
    data class PlayProgress(
        val currentPosition: Long,
        val duration: Long,
    )

    // 歌词行数据类
    data class LyricLine(
        val time: Long,
        val content: String,
        val translation: String? = null,
    )

    /**
     * 设置当前播放歌曲
     */
    fun setCurrentSong(song: Song) {
        _currentSongFlow.value = song
        checkLikeStatus(song.id)

        // 自动缓存当前播放的歌曲
        viewModelScope.launch {
            try {
                musicFileCache.autoCacheSong(song)
            } catch (e: Exception) {
                Log.e(TAG, "自动缓存歌曲失败: ${song.name}", e)
            }
        }
    }

    /**
     * 设置播放状态
     */
    fun setPlayingState(isPlaying: Boolean) {
        _isPlayingFlow.value = isPlaying
    }

    /**
     * 设置播放队列
     */
    fun setPlayQueue(queue: List<MediaItem>) {
        _playQueueFlow.value = queue
    }

    /**
     * 检查收藏状态
     * 参考ponymusic标准实现收藏状态检查逻辑
     * @param songId 歌曲ID
     */
    private fun checkLikeStatus(songId: Long) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "检查歌曲收藏状态: $songId")

                // 调用Repository检查收藏状态
                val isLiked = musicRepository.isLiked(songId)

                // 更新状态Flow
                _isLikedFlow.value = isLiked
                _isCurrentSongCollected.value = isLiked

                Log.d(TAG, "歌曲 $songId 收藏状态: $isLiked")
            } catch (e: Exception) {
                Log.e(TAG, "检查收藏状态失败: songId=$songId", e)
                // 发生错误时设置为未收藏状态
                _isLikedFlow.value = false
                _isCurrentSongCollected.value = false
            }
        }
    }

    /**
     * 切换收藏状态 - 参考ponymusic标准
     */
    fun toggleLike() {
        val currentSong = _currentSongFlow.value ?: return

        viewModelScope.launch {
            try {
                Log.d(TAG, "切换收藏状态: songId=${currentSong.id}")

                // 使用Repository的toggleLikeSong方法
                musicRepository.toggleLikeSong(currentSong.id).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            val newLikedState = result.data
                            _isLikedFlow.value = newLikedState
                            _isCurrentSongCollected.value = newLikedState
                            Log.d(TAG, "切换收藏状态成功: songId=${currentSong.id}, newState=$newLikedState")
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "切换收藏状态失败: ${result.message}")
                            // 可以在这里显示错误提示
                        }
                        is NetworkResult.Loading -> {
                            // 可以在这里显示加载状态
                            Log.d(TAG, "正在切换收藏状态...")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "切换收藏状态异常", e)
            }
        }
    }

    /**
     * 加载评论
     */
    fun loadComments(songId: Long) {
        viewModelScope.launch {
            try {
                val comments = musicRepository.getComments(songId)
                _commentsFlow.value = comments
                _commentCountFlow.value = comments.size

                // 分离热门评论
                val hotComments = comments.take(5)
                _hotCommentsFlow.value = hotComments
            } catch (e: Exception) {
                Log.e(TAG, "加载评论失败", e)
            }
        }
    }

    /**
     * 发送评论
     */
    fun sendComment(songId: Long, content: String) {
        viewModelScope.launch {
            try {
                val success = musicRepository.sendComment(songId, content)
                if (success) {
                    // 重新加载评论
                    loadComments(songId)
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送评论失败", e)
            }
        }
    }

    /**
     * 加载相似歌曲
     */
    fun loadSimilarSongs(songId: Long) {
        viewModelScope.launch {
            try {
                val songs = musicRepository.getSimilarSongs(songId)
                _similarSongsFlow.value = songs
            } catch (e: Exception) {
                Log.e(TAG, "加载相似歌曲失败", e)
            }
        }
    }

    /**
     * 清空播放队列
     * 参考ponymusic标准实现播放队列管理
     */
    fun clearPlayQueue() {
        Log.d(TAG, "清空播放队列")
        _playQueueFlow.value = emptyList()
        _currentMediaItemIndex.value = -1
    }

    /**
     * 添加歌曲到播放队列
     * @param mediaItems 要添加的媒体项列表
     */
    fun addToPlayQueue(mediaItems: List<MediaItem>) {
        val currentQueue = _playQueueFlow.value.toMutableList()
        val startIndex = currentQueue.size
        currentQueue.addAll(mediaItems)
        _playQueueFlow.value = currentQueue

        Log.d(TAG, "添加 ${mediaItems.size} 首歌曲到播放队列，队列总数: ${currentQueue.size}")

        // 如果队列之前为空，设置当前播放索引
        if (startIndex == 0 && mediaItems.isNotEmpty()) {
            _currentMediaItemIndex.value = 0
        }
    }

    /**
     * 插入歌曲到播放队列指定位置
     * @param index 插入位置
     * @param mediaItem 要插入的媒体项
     */
    fun insertToPlayQueue(index: Int, mediaItem: MediaItem) {
        val currentQueue = _playQueueFlow.value.toMutableList()
        val insertIndex = index.coerceIn(0, currentQueue.size)

        currentQueue.add(insertIndex, mediaItem)
        _playQueueFlow.value = currentQueue

        Log.d(TAG, "在位置 $insertIndex 插入歌曲: ${mediaItem.mediaId}")

        // 调整当前播放索引
        val currentIndex = _currentMediaItemIndex.value
        if (currentIndex >= insertIndex) {
            _currentMediaItemIndex.value = currentIndex + 1
        }
    }

    /**
     * 从播放队列移除歌曲
     * @param index 要移除的歌曲索引
     */
    fun removeFromPlayQueue(index: Int) {
        val currentQueue = _playQueueFlow.value.toMutableList()
        if (index !in currentQueue.indices) {
            Log.w(TAG, "移除播放队列歌曲失败：索引 $index 超出范围")
            return
        }

        val removedItem = currentQueue.removeAt(index)
        _playQueueFlow.value = currentQueue

        Log.d(TAG, "从播放队列移除歌曲: ${removedItem.mediaId}")

        // 调整当前播放索引
        val currentIndex = _currentMediaItemIndex.value
        when {
            index < currentIndex -> _currentMediaItemIndex.value = currentIndex - 1
            index == currentIndex -> {
                // 如果移除的是当前播放歌曲
                if (currentQueue.isEmpty()) {
                    _currentMediaItemIndex.value = -1
                } else {
                    // 保持在相同位置，但不超过队列长度
                    _currentMediaItemIndex.value = currentIndex.coerceAtMost(currentQueue.size - 1)
                }
            }
        }
    }

    /**
     * 在播放队列中移动歌曲位置（拖拽排序）
     * @param fromIndex 原始位置
     * @param toIndex 目标位置
     */
    fun moveInPlayQueue(fromIndex: Int, toIndex: Int) {
        val currentQueue = _playQueueFlow.value.toMutableList()
        if (fromIndex !in currentQueue.indices || toIndex !in currentQueue.indices) {
            Log.w(TAG, "移动播放队列歌曲失败：索引超出范围 ($fromIndex -> $toIndex)")
            return
        }

        if (fromIndex == toIndex) return

        val item = currentQueue.removeAt(fromIndex)
        currentQueue.add(toIndex, item)
        _playQueueFlow.value = currentQueue

        Log.d(TAG, "移动播放队列歌曲: $fromIndex -> $toIndex")

        // 调整当前播放索引
        val currentIndex = _currentMediaItemIndex.value
        when {
            currentIndex == fromIndex -> _currentMediaItemIndex.value = toIndex
            fromIndex < currentIndex && toIndex >= currentIndex -> _currentMediaItemIndex.value = currentIndex - 1
            fromIndex > currentIndex && toIndex <= currentIndex -> _currentMediaItemIndex.value = currentIndex + 1
        }
    }

    // 播放控制方法
    fun togglePlayPause() {
        val currentState = _playState.value
        _playState.value = when (currentState) {
            PlayState.PLAYING -> PlayState.PAUSED
            PlayState.PAUSED -> PlayState.PLAYING
            else -> PlayState.PLAYING
        }
    }

    /**
     * 播放上一首歌曲
     * 参考ponymusic标准实现播放模式逻辑
     */
    fun skipToPrevious() {
        val currentQueue = _playQueueFlow.value
        val currentIndex = _currentMediaItemIndex.value

        if (currentQueue.isEmpty()) {
            Log.w(TAG, "播放队列为空，无法播放上一首")
            return
        }

        val previousIndex = getPreviousSongIndex()
        if (previousIndex != -1) {
            _currentMediaItemIndex.value = previousIndex
            Log.d(TAG, "播放上一首: 索引 $currentIndex -> $previousIndex")
        } else {
            Log.d(TAG, "已是第一首歌曲")
        }
    }

    /**
     * 播放下一首歌曲
     * 参考ponymusic标准实现播放模式逻辑
     */
    fun skipToNext() {
        val currentQueue = _playQueueFlow.value
        val currentIndex = _currentMediaItemIndex.value

        if (currentQueue.isEmpty()) {
            Log.w(TAG, "播放队列为空，无法播放下一首")
            return
        }

        val nextIndex = getNextSongIndex()
        if (nextIndex != -1) {
            _currentMediaItemIndex.value = nextIndex
            Log.d(TAG, "播放下一首: 索引 $currentIndex -> $nextIndex")
        } else {
            Log.d(TAG, "已是最后一首歌曲")
        }
    }

    /**
     * 跳转到指定播放位置
     * @param position 播放位置（毫秒）
     */
    fun seekTo(position: Long) {
        Log.d(TAG, "跳转到播放位置: ${position}ms")
        // 更新播放进度
        val currentProgress = _playProgress.value
        _playProgress.value = currentProgress.copy(currentPosition = position)
    }

    /**
     * 切换播放模式
     * 参考ponymusic标准实现播放模式切换
     */
    fun togglePlayMode() {
        val currentMode = _playMode.value
        val nextMode = when (currentMode) {
            PlayMode.LOOP -> PlayMode.SHUFFLE
            PlayMode.SHUFFLE -> PlayMode.SINGLE
            PlayMode.SINGLE -> PlayMode.LOOP
        }

        _playMode.value = nextMode
        Log.d(TAG, "播放模式切换: $currentMode -> $nextMode")

        // 保存播放模式到SharedPreferences
        savePlayModeToPreferences(nextMode)
    }

    /**
     * 切换心动模式
     * 参考ponymusic标准实现心动模式功能
     * 包含登录状态检查、智能推荐获取、播放队列管理
     */
    fun toggleHeartMode() {
        viewModelScope.launch {
            try {
                val currentState = _heartModeEnabled.value
                val newState = !currentState

                Log.d(TAG, "心动模式切换: $currentState -> $newState")

                if (newState) {
                    // 开启心动模式
                    enableIntelligenceMode()
                } else {
                    // 关闭心动模式
                    disableIntelligenceMode()
                }
            } catch (e: Exception) {
                Log.e(TAG, "切换心动模式失败", e)
                _errorMessage.value = "心动模式切换失败: ${e.message}"
            }
        }
    }

    /**
     * 开启心动模式
     * 包含完整的登录检查、API调用、播放队列管理流程
     */
    private suspend fun enableIntelligenceMode() {
        try {
            // 1. 检查登录状态
            val isLoggedIn = checkLoginStatus()
            if (!isLoggedIn) {
                _errorMessage.value = "请先登录"
                Log.w(TAG, "用户未登录，无法开启心动模式")
                return
            }

            // 2. 检查当前播放歌曲
            val currentSong = _currentSongFlow.value
            if (currentSong == null) {
                _errorMessage.value = "请先播放一首歌曲"
                Log.w(TAG, "当前没有播放歌曲，无法开启心动模式")
                return
            }

            // 3. 获取当前播放列表ID（使用默认值或从当前上下文获取）
            val playlistId = getCurrentPlaylistId() ?: 24381616L // 使用默认歌单ID

            Log.d(TAG, "开启心动模式: songId=${currentSong.id}, playlistId=$playlistId")

            // 4. 调用心动模式API获取智能推荐
            musicRepository.getIntelligenceList(
                songId = currentSong.id,
                playlistId = playlistId,
                startSongId = currentSong.id
            ).collect { result ->
                when (result) {
                    is NetworkResult.Loading -> {
                        Log.d(TAG, "正在获取心动模式推荐...")
                    }
                    is NetworkResult.Success -> {
                        val intelligenceSongs = result.data
                        if (intelligenceSongs.isNotEmpty()) {
                            // 5. 成功获取推荐歌曲，更新状态
                            _heartModeEnabled.value = true
                            _heartModeRecommendations.value = intelligenceSongs

                            // 6. 将推荐歌曲添加到播放队列（标记为心动模式歌曲）
                            val mediaItems = intelligenceSongs.map { song ->
                                createIntelligenceModeMediaItem(song)
                            }

                            // 7. 添加到播放队列
                            addToPlayQueue(mediaItems)

                            Log.d(TAG, "心动模式开启成功，获得 ${intelligenceSongs.size} 首推荐歌曲")
                            _errorMessage.value = "已开启心动模式，为您推荐相似歌曲"
                        } else {
                            Log.w(TAG, "心动模式API返回空数据")
                            _errorMessage.value = "暂无相似歌曲推荐"
                        }
                    }
                    is NetworkResult.Error -> {
                        Log.e(TAG, "获取心动模式推荐失败: ${result.message}")
                        _errorMessage.value = "获取推荐歌曲失败: ${result.message}"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "开启心动模式失败", e)
            _errorMessage.value = "开启心动模式失败: ${e.message}"
        }
    }

    /**
     * 关闭心动模式
     * 清理心动模式相关状态和播放队列
     */
    private suspend fun disableIntelligenceMode() {
        try {
            Log.d(TAG, "关闭心动模式")

            // 1. 更新心动模式状态
            _heartModeEnabled.value = false

            // 2. 清空心动模式推荐歌曲
            _heartModeRecommendations.value = emptyList()

            // 3. 从播放队列中移除心动模式歌曲
            removeIntelligenceModeSongsFromQueue()

            // 4. 如果当前正在播放心动模式歌曲，停止播放
            val currentQueue = _playQueueFlow.value
            val currentIndex = _currentMediaItemIndex.value
            if (currentIndex >= 0 && currentIndex < currentQueue.size) {
                val currentItem = currentQueue[currentIndex]
                if (isIntelligenceModeMediaItem(currentItem)) {
                    // 停止当前播放
                    _playState.value = PlayState.PAUSED
                    Log.d(TAG, "停止播放心动模式歌曲")
                }
            }

            Log.d(TAG, "心动模式关闭成功")
            _errorMessage.value = "已退出心动模式"
        } catch (e: Exception) {
            Log.e(TAG, "关闭心动模式失败", e)
            _errorMessage.value = "退出心动模式失败: ${e.message}"
        }
    }

    /**
     * 检查登录状态
     * @return 是否已登录
     */
    private suspend fun checkLoginStatus(): Boolean {
        return try {
            // TODO: 实现实际的登录状态检查
            // 这里应该调用 musicRepository.checkLoginStatus() 或类似方法
            Log.d(TAG, "检查登录状态...")
            // 暂时返回true，实际应该检查用户登录状态
            true
        } catch (e: Exception) {
            Log.e(TAG, "检查登录状态失败", e)
            false
        }
    }

    /**
     * 获取当前播放列表ID
     * @return 播放列表ID，如果没有则返回null
     */
    private fun getCurrentPlaylistId(): Long? {
        // TODO: 实现获取当前播放列表ID的逻辑
        // 这里应该从当前上下文或播放状态中获取播放列表ID
        return 24381616L // 使用默认歌单ID作为示例
    }

    /**
     * 创建心动模式MediaItem
     * 为心动模式歌曲添加特殊标记
     * @param song 歌曲信息
     * @return 带有心动模式标记的MediaItem
     */
    private fun createIntelligenceModeMediaItem(song: Song): MediaItem {
        return MediaItem.Builder()
            .setMediaId(song.id.toString())
            .setUri(Uri.parse("${Constants.BASE_URL}/song/url?id=${song.id}"))
            .setMediaMetadata(
                androidx.media3.common.MediaMetadata.Builder()
                    .setTitle(song.name)
                    .setArtist(song.ar?.joinToString(", ") { it.name } ?: "未知艺术家")
                    .setAlbumTitle(song.al?.name ?: "未知专辑")
                    .setArtworkUri(Uri.parse(song.al?.picUrl ?: ""))
                    .setExtras(
                        android.os.Bundle().apply {
                            putBoolean("isIntelligenceMode", true) // 标记为心动模式歌曲
                            putLong("duration", song.dt)
                        }
                    )
                    .build()
            )
            .build()
    }

    /**
     * 检查MediaItem是否为心动模式歌曲
     * @param mediaItem 媒体项
     * @return 是否为心动模式歌曲
     */
    private fun isIntelligenceModeMediaItem(mediaItem: MediaItem): Boolean {
        return mediaItem.mediaMetadata.extras?.getBoolean("isIntelligenceMode", false) ?: false
    }

    /**
     * 从播放队列中移除所有心动模式歌曲
     */
    private fun removeIntelligenceModeSongsFromQueue() {
        val currentQueue = _playQueueFlow.value.toMutableList()
        val iterator = currentQueue.iterator()
        var removedCount = 0
        var currentIndex = _currentMediaItemIndex.value

        while (iterator.hasNext()) {
            val item = iterator.next()
            if (isIntelligenceModeMediaItem(item)) {
                val itemIndex = currentQueue.indexOf(item)
                iterator.remove()
                removedCount++

                // 调整当前播放索引
                if (itemIndex < currentIndex) {
                    currentIndex--
                } else if (itemIndex == currentIndex) {
                    // 如果移除的是当前播放歌曲，重置索引
                    currentIndex = -1
                }
            }
        }

        if (removedCount > 0) {
            _playQueueFlow.value = currentQueue
            _currentMediaItemIndex.value = if (currentQueue.isEmpty()) -1 else currentIndex.coerceAtMost(currentQueue.size - 1)
            Log.d(TAG, "从播放队列移除了 $removedCount 首心动模式歌曲")
        }
    }

    /**
     * 加载心动模式推荐歌曲（保留向后兼容）
     * @param songId 当前歌曲ID
     */
    fun loadHeartModeSongs(songId: Long) {
        viewModelScope.launch {
            kotlin.runCatching {
                Log.d(TAG, "开始加载心动模式推荐歌曲: songId=$songId")

                // 获取相似歌曲作为心动模式推荐
                val similarSongs = musicRepository.getSimilarSongs(songId)

                if (similarSongs.isNotEmpty()) {
                    Log.d(TAG, "获取到 ${similarSongs.size} 首心动模式推荐歌曲")

                    // 更新心动模式推荐歌曲列表
                    _heartModeRecommendations.value = similarSongs

                    // 同时更新相似歌曲列表（向后兼容）
                    _similarSongsFlow.value = similarSongs

                    // 将推荐歌曲转换为MediaItem并添加到播放队列
                    val mediaItems = similarSongs.map { song ->
                        createIntelligenceModeMediaItem(song)
                    }

                    // 清空当前队列并添加推荐歌曲
                    clearPlayQueue()
                    addToPlayQueue(mediaItems)

                    Log.d(TAG, "心动模式推荐歌曲已添加到播放队列")
                } else {
                    Log.w(TAG, "未获取到心动模式推荐歌曲")
                    _heartModeRecommendations.value = emptyList()
                }
            }.onFailure { exception ->
                Log.e(TAG, "加载心动模式推荐歌曲失败", exception)
                // 发生错误时关闭心动模式
                _heartModeEnabled.value = false
            }
        }
    }

    /**
     * 获取下一首歌曲索引
     * 根据当前播放模式返回下一首歌曲的索引
     * @return 下一首歌曲索引，-1表示没有下一首
     */
    private fun getNextSongIndex(): Int {
        val currentQueue = _playQueueFlow.value
        val currentIndex = _currentMediaItemIndex.value
        val playMode = _playMode.value

        if (currentQueue.isEmpty() || currentIndex < 0) return -1

        return when (playMode) {
            PlayMode.SINGLE -> currentIndex // 单曲循环，保持当前索引
            PlayMode.LOOP -> {
                // 列表循环
                if (currentIndex >= currentQueue.size - 1) 0 else currentIndex + 1
            }
            PlayMode.SHUFFLE -> {
                // 随机播放
                if (currentQueue.size <= 1) {
                    currentIndex
                } else {
                    var randomIndex: Int
                    do {
                        randomIndex = (0 until currentQueue.size).random()
                    } while (randomIndex == currentIndex && currentQueue.size > 1)
                    randomIndex
                }
            }
        }
    }

    /**
     * 获取上一首歌曲索引
     * 根据当前播放模式返回上一首歌曲的索引
     * @return 上一首歌曲索引，-1表示没有上一首
     */
    private fun getPreviousSongIndex(): Int {
        val currentQueue = _playQueueFlow.value
        val currentIndex = _currentMediaItemIndex.value
        val playMode = _playMode.value

        if (currentQueue.isEmpty() || currentIndex < 0) return -1

        return when (playMode) {
            PlayMode.SINGLE -> currentIndex // 单曲循环，保持当前索引
            PlayMode.LOOP -> {
                // 列表循环
                if (currentIndex <= 0) currentQueue.size - 1 else currentIndex - 1
            }
            PlayMode.SHUFFLE -> {
                // 随机播放（简化实现，实际应该维护播放历史）
                if (currentQueue.size <= 1) {
                    currentIndex
                } else {
                    var randomIndex: Int
                    do {
                        randomIndex = (0 until currentQueue.size).random()
                    } while (randomIndex == currentIndex && currentQueue.size > 1)
                    randomIndex
                }
            }
        }
    }

    /**
     * 保存播放模式到SharedPreferences
     * @param playMode 播放模式
     */
    private fun savePlayModeToPreferences(playMode: PlayMode) {
        // TODO: 实现播放模式持久化
        Log.d(TAG, "保存播放模式: $playMode")
    }

    /**
     * 获取收藏歌曲列表
     * 参考ponymusic标准实现收藏功能
     * @return Flow<NetworkResult<List<Song>>>
     */
    fun getLikedSongs(): Flow<NetworkResult<List<Song>>> {
        return musicRepository.getLikedSongsDetailed()
    }

    // 搜索相关方法
    fun searchSongs(query: String) {
        _isSearching.value = true
        viewModelScope.launch {
            try {
                musicRepository.searchSongs(query).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            _searchResults.value = result.data
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "搜索歌曲失败: ${result.message}")
                        }
                        is NetworkResult.Loading -> {
                            // 保持加载状态
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "搜索歌曲失败", e)
            } finally {
                _isSearching.value = false
            }
        }
    }

    fun getSearchSuggestions(query: String) {
        viewModelScope.launch {
            try {
                musicRepository.getSearchSuggestions(query).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            _searchItems.value = result.data
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "获取搜索建议失败: ${result.message}")
                        }
                        is NetworkResult.Loading -> {
                            // 保持加载状态
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取搜索建议失败", e)
            }
        }
    }

    fun clearSearchResults() {
        _searchResults.value = emptyList()
        _searchItems.value = emptyList()
    }

    fun deleteSearchHistory() {
        // TODO: 实现删除搜索历史
        Log.d(TAG, "deleteSearchHistory called")
    }

    // 歌曲详情相关
    suspend fun getSongDetail(songId: Long): Song? {
        return try {
            val song = musicRepository.getSongDetail(songId)
            song?.let { setCurrentSong(it) }
            song
        } catch (e: Exception) {
            Log.e(TAG, "获取歌曲详情失败", e)
            null
        }
    }

    /**
     * 加载歌词信息
     * 参考ponymusic标准实现歌词加载和时间同步
     * @param songId 歌曲ID
     */
    fun loadLyricInfo(songId: Long) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始加载歌词: songId=$songId")

                val lyricResponse = musicRepository.getLyric(songId)
                // 解析歌词响应并转换为LyricLine列表
                val lyricLines = parseLyricResponse(lyricResponse?.lrc?.lyric ?: "")
                _lyrics.value = lyricLines

                Log.d(TAG, "歌词加载完成，共 ${lyricLines.size} 行")

                // 如果有翻译歌词，也一并处理
                // TODO: 实现翻译歌词的加载和合并
            } catch (e: Exception) {
                Log.e(TAG, "加载歌词失败: songId=$songId", e)
                _lyrics.value = emptyList()
            }
        }
    }

    /**
     * 根据当前播放时间获取当前歌词行
     * @param currentTimeMs 当前播放时间（毫秒）
     * @return 当前歌词行索引，-1表示没有匹配的歌词
     */
    fun getCurrentLyricIndex(currentTimeMs: Long): Int {
        val lyrics = _lyrics.value
        if (lyrics.isEmpty()) return -1

        // 找到最后一个时间小于等于当前时间的歌词行
        var currentIndex = -1
        for (i in lyrics.indices) {
            if (lyrics[i].time <= currentTimeMs) {
                currentIndex = i
            } else {
                break
            }
        }

        return currentIndex
    }

    /**
     * 获取指定时间范围内的歌词行
     * @param centerTimeMs 中心时间（毫秒）
     * @param lineCount 返回的歌词行数（默认5行）
     * @return 歌词行列表
     */
    fun getLyricsAroundTime(centerTimeMs: Long, lineCount: Int = 5): List<LyricLine> {
        val lyrics = _lyrics.value
        if (lyrics.isEmpty()) return emptyList()

        val currentIndex = getCurrentLyricIndex(centerTimeMs)
        if (currentIndex == -1) return lyrics.take(lineCount)

        val halfCount = lineCount / 2
        val startIndex = (currentIndex - halfCount).coerceAtLeast(0)
        val endIndex = (currentIndex + halfCount + 1).coerceAtMost(lyrics.size)

        return lyrics.subList(startIndex, endIndex)
    }

    /**
     * 解析LRC格式歌词内容
     * 参考ponymusic标准实现完整的歌词解析功能
     * @param lyricContent LRC格式的歌词内容
     * @return 按时间排序的歌词行列表
     */
    private fun parseLyricResponse(lyricContent: String): List<LyricLine> {
        if (lyricContent.isBlank()) {
            Log.d(TAG, "歌词内容为空")
            return emptyList()
        }

        val lyricLines = mutableListOf<LyricLine>()

        try {
            Log.d(TAG, "开始解析歌词，内容长度: ${lyricContent.length}")

            val lines = lyricContent.split("\n", "\r\n")
            // 支持多种时间格式：[mm:ss.xx] 或 [mm:ss.xxx]
            val timePattern = Regex("\\[(\\d{1,2}):(\\d{2})\\.(\\d{2,3})\\]")

            for (line in lines) {
                val trimmedLine = line.trim()
                if (trimmedLine.isEmpty()) continue

                // 查找所有时间标签
                val timeMatches = timePattern.findAll(trimmedLine).toList()
                if (timeMatches.isEmpty()) continue

                // 提取歌词文本（移除所有时间标签）
                val lyricText = trimmedLine.replace(timePattern, "").trim()
                if (lyricText.isEmpty()) continue

                // 为每个时间标签创建歌词行
                for (match in timeMatches) {
                    val minutes = match.groupValues[1].toIntOrNull() ?: 0
                    val seconds = match.groupValues[2].toIntOrNull() ?: 0
                    val millisStr = match.groupValues[3]

                    // 处理毫秒：2位数需要*10，3位数直接使用
                    val milliseconds = when (millisStr.length) {
                        2 -> millisStr.toIntOrNull()?.times(10) ?: 0
                        3 -> millisStr.toIntOrNull() ?: 0
                        else -> 0
                    }

                    val timeMs = (minutes * 60 * 1000 + seconds * 1000 + milliseconds).toLong()
                    lyricLines.add(LyricLine(timeMs, lyricText))
                }
            }

            // 按时间排序
            lyricLines.sortBy { it.time }

            Log.d(TAG, "歌词解析完成，共解析出 ${lyricLines.size} 行歌词")
            return lyricLines
        } catch (e: Exception) {
            Log.e(TAG, "歌词解析失败", e)
            return emptyList()
        }
    }

    // 收藏相关
    fun toggleCollect() {
        toggleLike()
    }

    // 播放列表管理
    fun updatePlayQueue(songs: List<Song>) {
        val mediaItems = songs.map { song ->
            MediaItem.Builder()
                .setMediaId(song.id.toString())
                .setUri(song.playUrl ?: "")
                .build()
        }
        setPlayQueue(mediaItems)
    }

    fun playAtIndex(index: Int) {
        // TODO: 实现播放指定索引歌曲
        Log.d(TAG, "playAtIndex: $index")
    }

    fun clearPlaylist() {
        clearPlayQueue()
    }

    fun shufflePlaylist() {
        val currentQueue = _playQueueFlow.value.shuffled()
        _playQueueFlow.value = currentQueue
    }

    fun removeFromPlaylist(index: Int) {
        removeFromPlayQueue(index)
    }

    // 搜索交互
    fun onSearchItemClick(item: String) {
        searchSongs(item)
    }

    fun playSearchResult(song: Song) {
        setCurrentSong(song)
        _playState.value = PlayState.PLAYING
    }

    // 错误处理
    fun showErrorDialog(message: String) {
        Log.e(TAG, "Error: $message")
    }

    // PlayQueueDialogFragment需要的方法
    fun seekToQueueItem(position: Int) {
        _currentMediaItemIndex.value = position
        Log.d(TAG, "seekToQueueItem: $position")
    }

    fun toggleRepeatMode() {
        _repeatMode.value = when (_repeatMode.value) {
            0 -> 1 // OFF -> ONE
            1 -> 2 // ONE -> ALL
            2 -> 0 // ALL -> OFF
            else -> 0
        }
    }

    fun shuffleQueue() {
        val currentQueue = _playQueueFlow.value.shuffled()
        _playQueueFlow.value = currentQueue
    }

    fun removeFromQueue(position: Int) {
        removeFromPlayQueue(position)
    }

    fun clearQueue() {
        clearPlayQueue()
    }

    /**
     * 检查歌曲是否已缓存
     * @param songId 歌曲ID
     * @return 是否已缓存
     */
    suspend fun isSongCached(songId: Long): Boolean {
        return musicFileCache.isCached(songId)
    }

    /**
     * 获取缓存文件路径
     * @param songId 歌曲ID
     * @return 缓存文件路径，如果未缓存则返回null
     */
    suspend fun getCachedFilePath(songId: Long): String? {
        return musicFileCache.getCachedFilePath(songId)
    }

    /**
     * 手动缓存歌曲
     * @param song 歌曲信息
     */
    fun cacheSong(song: Song) {
        viewModelScope.launch {
            try {
                val success = musicFileCache.manualCacheSong(song) { progress ->
                    Log.d(TAG, "缓存进度: ${song.name} - ${(progress.progress * 100).toInt()}%")
                    // TODO: 更新UI进度
                }

                if (success) {
                    Log.d(TAG, "手动缓存成功: ${song.name}")
                } else {
                    Log.w(TAG, "手动缓存失败: ${song.name}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "手动缓存歌曲失败: ${song.name}", e)
            }
        }
    }

    /**
     * 删除歌曲缓存
     * @param songId 歌曲ID
     */
    fun deleteSongCache(songId: Long) {
        viewModelScope.launch {
            try {
                val success = musicFileCache.deleteCachedSong(songId)
                if (success) {
                    Log.d(TAG, "删除缓存成功: $songId")
                } else {
                    Log.w(TAG, "删除缓存失败: $songId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除缓存失败: $songId", e)
            }
        }
    }
}
