package com.example.aimusicplayer.ui.player

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.MusicApplication
import com.example.aimusicplayer.databinding.PagePlayerCommentBinding
import com.example.aimusicplayer.ui.adapter.CommentAdapter
import com.example.aimusicplayer.viewmodel.CommentViewModel
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 评论页面Fragment
 * 用于在ViewPager2中显示评论
 */
@AndroidEntryPoint
class CommentPageFragment : Fragment() {

    private val TAG = "CommentPageFragment"
    private var _binding: PagePlayerCommentBinding? = null
    private val binding get() = _binding!!

    private val playerViewModel: PlayerViewModel by activityViewModels()
    private val commentViewModel: CommentViewModel by viewModels()
    private lateinit var adapter: CommentAdapter

    @Inject
    lateinit var userService: com.example.aimusicplayer.service.UserService

    // 分页加载相关变量
    private var currentPage = 1
    private var isLoadingMore = false
    private var hasMoreData = true
    private val pageSize = 20
    private val loadMoreThreshold = 3 // 距离底部3个item时开始加载

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PagePlayerCommentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        setupObservers()
    }

    private fun setupViews() {
        // 设置返回按钮
        binding.btnBackToLyric?.setOnClickListener {
            // 返回到歌词页面
            (parentFragment as? PlayerFragment)?.switchToLyricPage()
        }

        // 设置RecyclerView
        setupRecyclerView()

        // 设置下拉刷新
        setupSwipeRefresh()

        // 设置发送评论按钮
        binding.btnSendComment?.setOnClickListener {
            sendComment()
        }
    }

    private fun setupRecyclerView() {
        adapter = CommentAdapter(
            onLikeClick = { comment ->
                commentViewModel.likeComment(comment.commentId)
            }
        )

        binding.recyclerViewComments.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            setHasFixedSize(true)
            // 车载优化：增加缓存大小
            setItemViewCacheSize(20)

            // 添加滚动监听器，实现自动加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只在向下滚动时检查
                    if (dy > 0) {
                        checkAndLoadMore(recyclerView)
                    }
                }
            })
        }
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setColorSchemeResources(
            com.example.aimusicplayer.R.color.colorPrimary,
            com.example.aimusicplayer.R.color.colorAccent,
            com.example.aimusicplayer.R.color.colorPrimaryDark
        )

        binding.swipeRefreshLayout.setOnRefreshListener {
            loadComments()
        }
    }

    private fun setupObservers() {
        // 观察当前歌曲变化，加载对应评论
        viewLifecycleOwner.lifecycleScope.launch {
            playerViewModel.currentSong.collect { song ->
                if (song != null) {
                    resetPagination()
                    loadComments()
                } else {
                    // 清空评论列表
                    adapter.submitList(emptyList())
                    resetPagination()
                }
            }
        }

        // 观察评论列表
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.comments.collect { comments ->
                adapter.submitList(comments)
                binding.swipeRefreshLayout.isRefreshing = false

                // 显示/隐藏空状态
                if (comments.isEmpty()) {
                    binding.textEmptyComment.visibility = View.VISIBLE
                    binding.recyclerViewComments.visibility = View.GONE
                } else {
                    binding.textEmptyComment.visibility = View.GONE
                    binding.recyclerViewComments.visibility = View.VISIBLE
                }
            }
        }

        // 观察加载状态
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.loading.collect { isLoading ->
                if (!binding.swipeRefreshLayout.isRefreshing) {
                    binding.progressBar?.visibility = if (isLoading) View.VISIBLE else View.GONE
                }
            }
        }

        // 观察加载更多状态（如果ViewModel支持的话）
        // 注意：这里需要根据实际的CommentViewModel实现来调整
        // viewLifecycleOwner.lifecycleScope.launch {
        //     commentViewModel.isLoadingMore.collect { isLoadingMore ->
        //         <EMAIL> = isLoadingMore
        //         Log.d(TAG, "加载更多状态: $isLoadingMore")
        //     }
        // }

        // 观察是否有更多数据（如果ViewModel支持的话）
        // viewLifecycleOwner.lifecycleScope.launch {
        //     commentViewModel.hasMoreData.collect { hasMore ->
        //         hasMoreData = hasMore
        //         Log.d(TAG, "是否有更多数据: $hasMore")
        //     }
        // }

        // 观察错误信息
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.errorMessage.collect { message ->
                if (!message.isNullOrEmpty()) {
                    Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                    binding.swipeRefreshLayout.isRefreshing = false
                }
            }
        }

        // 观察评论发送成功事件
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.commentSent.collect { sent ->
                if (sent) {
                    // 清空输入框
                    binding.editTextComment?.setText("")
                    // 显示成功提示
                    Toast.makeText(requireContext(), "评论发送成功", Toast.LENGTH_SHORT).show()
                    // 重置状态
                    commentViewModel.resetCommentSentState()
                }
            }
        }
    }

    /**
     * 加载评论（首次加载）
     */
    private fun loadComments() {
        val songId = playerViewModel.currentSong.value?.id
        if (songId != null) {
            Log.d(TAG, "开始加载评论，歌曲ID: $songId")
            commentViewModel.loadComments(songId)
        }
    }

    /**
     * 检查并加载更多评论
     */
    private fun checkAndLoadMore(recyclerView: RecyclerView) {
        if (isLoadingMore || !hasMoreData) {
            return
        }

        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
        val visibleItemCount = layoutManager.childCount
        val totalItemCount = layoutManager.itemCount
        val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

        // 当滚动到距离底部loadMoreThreshold个item时，开始加载更多
        if ((visibleItemCount + firstVisibleItemPosition) >= (totalItemCount - loadMoreThreshold)) {
            loadMoreComments()
        }
    }

    /**
     * 加载更多评论
     */
    private fun loadMoreComments() {
        if (isLoadingMore || !hasMoreData) {
            return
        }

        val songId = playerViewModel.currentSong.value?.id
        if (songId != null) {
            currentPage++
            isLoadingMore = true
            Log.d(TAG, "开始加载更多评论，页码: $currentPage")

            // 这里应该调用ViewModel的加载更多方法
            // 由于当前的CommentViewModel可能不支持分页，我们先模拟实现
            commentViewModel.loadComments(songId)

            // 模拟加载完成后重置状态
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                isLoadingMore = false
                // 如果没有更多数据，可以设置hasMoreData = false
            }, 1000)
        }
    }

    /**
     * 重置分页状态
     */
    private fun resetPagination() {
        currentPage = 1
        isLoadingMore = false
        hasMoreData = true
        Log.d(TAG, "重置分页状态")
    }

    private fun sendComment() {
        // 检查登录状态
        if (!checkLoginStatusForFeature("评论功能")) {
            return
        }

        val content = binding.editTextComment?.text?.toString()?.trim()
        if (content.isNullOrEmpty()) {
            Toast.makeText(requireContext(), "请输入评论内容", Toast.LENGTH_SHORT).show()
            return
        }

        val songId = playerViewModel.currentSong.value?.id
        if (songId != null) {
            commentViewModel.sendComment(songId, content)
        } else {
            Toast.makeText(requireContext(), "当前没有播放歌曲", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 检查登录状态，如果未登录则显示提示
     * @param featureName 功能名称，用于显示提示信息
     * @return 是否已登录
     */
    private fun checkLoginStatusForFeature(featureName: String): Boolean {
        return try {
            if (userService.isLogin()) {
                true
            } else {
                // 显示游客状态提示
                Toast.makeText(
                    requireContext(),
                    "请先登录后使用$featureName",
                    Toast.LENGTH_SHORT
                ).show()
                false
            }
        } catch (e: Exception) {
            Log.e("CommentPageFragment", "检查登录状态失败", e)
            Toast.makeText(
                requireContext(),
                "登录状态检查失败，请稍后重试",
                Toast.LENGTH_SHORT
            ).show()
            false
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
