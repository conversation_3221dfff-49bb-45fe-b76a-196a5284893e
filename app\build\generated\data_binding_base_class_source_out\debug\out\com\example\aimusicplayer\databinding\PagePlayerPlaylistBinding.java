// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PagePlayerPlaylistBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBackToLyric;

  @NonNull
  public final ImageButton btnClearPlaylist;

  @NonNull
  public final ImageButton btnShufflePlaylist;

  @NonNull
  public final ImageView imagePlayMode;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final LinearLayout layoutPlayMode;

  @NonNull
  public final RecyclerView recyclerViewPlaylist;

  @NonNull
  public final TextView textPlayMode;

  @NonNull
  public final TextView textSongCount;

  private PagePlayerPlaylistBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnBackToLyric, @NonNull ImageButton btnClearPlaylist,
      @NonNull ImageButton btnShufflePlaylist, @NonNull ImageView imagePlayMode,
      @NonNull LinearLayout layoutEmptyState, @NonNull LinearLayout layoutPlayMode,
      @NonNull RecyclerView recyclerViewPlaylist, @NonNull TextView textPlayMode,
      @NonNull TextView textSongCount) {
    this.rootView = rootView;
    this.btnBackToLyric = btnBackToLyric;
    this.btnClearPlaylist = btnClearPlaylist;
    this.btnShufflePlaylist = btnShufflePlaylist;
    this.imagePlayMode = imagePlayMode;
    this.layoutEmptyState = layoutEmptyState;
    this.layoutPlayMode = layoutPlayMode;
    this.recyclerViewPlaylist = recyclerViewPlaylist;
    this.textPlayMode = textPlayMode;
    this.textSongCount = textSongCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PagePlayerPlaylistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PagePlayerPlaylistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.page_player_playlist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PagePlayerPlaylistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back_to_lyric;
      ImageButton btnBackToLyric = ViewBindings.findChildViewById(rootView, id);
      if (btnBackToLyric == null) {
        break missingId;
      }

      id = R.id.btn_clear_playlist;
      ImageButton btnClearPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (btnClearPlaylist == null) {
        break missingId;
      }

      id = R.id.btn_shuffle_playlist;
      ImageButton btnShufflePlaylist = ViewBindings.findChildViewById(rootView, id);
      if (btnShufflePlaylist == null) {
        break missingId;
      }

      id = R.id.image_play_mode;
      ImageView imagePlayMode = ViewBindings.findChildViewById(rootView, id);
      if (imagePlayMode == null) {
        break missingId;
      }

      id = R.id.layout_empty_state;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.layout_play_mode;
      LinearLayout layoutPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (layoutPlayMode == null) {
        break missingId;
      }

      id = R.id.recycler_view_playlist;
      RecyclerView recyclerViewPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPlaylist == null) {
        break missingId;
      }

      id = R.id.text_play_mode;
      TextView textPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (textPlayMode == null) {
        break missingId;
      }

      id = R.id.text_song_count;
      TextView textSongCount = ViewBindings.findChildViewById(rootView, id);
      if (textSongCount == null) {
        break missingId;
      }

      return new PagePlayerPlaylistBinding((LinearLayout) rootView, btnBackToLyric,
          btnClearPlaylist, btnShufflePlaylist, imagePlayMode, layoutEmptyState, layoutPlayMode,
          recyclerViewPlaylist, textPlayMode, textSongCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
