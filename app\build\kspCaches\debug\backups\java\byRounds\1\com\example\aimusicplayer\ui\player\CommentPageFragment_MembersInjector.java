package com.example.aimusicplayer.ui.player;

import com.example.aimusicplayer.service.UserService;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CommentPageFragment_MembersInjector implements MembersInjector<CommentPageFragment> {
  private final Provider<UserService> userServiceProvider;

  public CommentPageFragment_MembersInjector(Provider<UserService> userServiceProvider) {
    this.userServiceProvider = userServiceProvider;
  }

  public static MembersInjector<CommentPageFragment> create(
      Provider<UserService> userServiceProvider) {
    return new CommentPageFragment_MembersInjector(userServiceProvider);
  }

  @Override
  public void injectMembers(CommentPageFragment instance) {
    injectUserService(instance, userServiceProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.player.CommentPageFragment.userService")
  public static void injectUserService(CommentPageFragment instance, UserService userService) {
    instance.userService = userService;
  }
}
