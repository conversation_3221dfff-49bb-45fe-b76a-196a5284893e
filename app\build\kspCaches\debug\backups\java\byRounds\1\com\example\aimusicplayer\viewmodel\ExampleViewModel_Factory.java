package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExampleViewModel_Factory implements Factory<ExampleViewModel> {
  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public ExampleViewModel_Factory(Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public ExampleViewModel get() {
    return newInstance(musicRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static ExampleViewModel_Factory create(Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new ExampleViewModel_Factory(musicRepositoryProvider, userRepositoryProvider);
  }

  public static ExampleViewModel newInstance(MusicRepository musicRepository,
      UserRepository userRepository) {
    return new ExampleViewModel(musicRepository, userRepository);
  }
}
