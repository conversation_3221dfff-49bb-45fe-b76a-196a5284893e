package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.service.UserService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginViewModel_Factory implements Factory<LoginViewModel> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<UserService> userServiceProvider;

  public LoginViewModel_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<UserService> userServiceProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.userServiceProvider = userServiceProvider;
  }

  @Override
  public LoginViewModel get() {
    return newInstance(userRepositoryProvider.get(), userServiceProvider.get());
  }

  public static LoginViewModel_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<UserService> userServiceProvider) {
    return new LoginViewModel_Factory(userRepositoryProvider, userServiceProvider);
  }

  public static LoginViewModel newInstance(UserRepository userRepository, UserService userService) {
    return new LoginViewModel(userRepository, userService);
  }
}
