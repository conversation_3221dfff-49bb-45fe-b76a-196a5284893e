package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserProfileViewModel_Factory implements Factory<UserProfileViewModel> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UserProfileViewModel_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UserProfileViewModel get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static UserProfileViewModel_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UserProfileViewModel_Factory(userRepositoryProvider);
  }

  public static UserProfileViewModel newInstance(UserRepository userRepository) {
    return new UserProfileViewModel(userRepository);
  }
}
