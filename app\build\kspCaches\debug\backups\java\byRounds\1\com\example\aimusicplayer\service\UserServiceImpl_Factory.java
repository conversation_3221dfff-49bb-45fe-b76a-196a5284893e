package com.example.aimusicplayer.service;

import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.data.source.ApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserServiceImpl_Factory implements Factory<UserServiceImpl> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<ApiService> apiServiceProvider;

  public UserServiceImpl_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<ApiService> apiServiceProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.apiServiceProvider = apiServiceProvider;
  }

  @Override
  public UserServiceImpl get() {
    return newInstance(userRepositoryProvider.get(), apiServiceProvider.get());
  }

  public static UserServiceImpl_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<ApiService> apiServiceProvider) {
    return new UserServiceImpl_Factory(userRepositoryProvider, apiServiceProvider);
  }

  public static UserServiceImpl newInstance(UserRepository userRepository, ApiService apiService) {
    return new UserServiceImpl(userRepository, apiService);
  }
}
