package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.CommentRepository;
import com.example.aimusicplayer.data.repository.MusicRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CommentViewModel_Factory implements Factory<CommentViewModel> {
  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<CommentRepository> commentRepositoryProvider;

  public CommentViewModel_Factory(Provider<MusicRepository> musicRepositoryProvider,
      Provider<CommentRepository> commentRepositoryProvider) {
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.commentRepositoryProvider = commentRepositoryProvider;
  }

  @Override
  public CommentViewModel get() {
    return newInstance(musicRepositoryProvider.get(), commentRepositoryProvider.get());
  }

  public static CommentViewModel_Factory create(Provider<MusicRepository> musicRepositoryProvider,
      Provider<CommentRepository> commentRepositoryProvider) {
    return new CommentViewModel_Factory(musicRepositoryProvider, commentRepositoryProvider);
  }

  public static CommentViewModel newInstance(MusicRepository musicRepository,
      CommentRepository commentRepository) {
    return new CommentViewModel(musicRepository, commentRepository);
  }
}
