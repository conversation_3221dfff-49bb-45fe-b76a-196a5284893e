package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.cache.MusicFileCache;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerViewModel_Factory implements Factory<PlayerViewModel> {
  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<MusicFileCache> musicFileCacheProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public PlayerViewModel_Factory(Provider<MusicRepository> musicRepositoryProvider,
      Provider<MusicFileCache> musicFileCacheProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.musicFileCacheProvider = musicFileCacheProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public PlayerViewModel get() {
    return newInstance(musicRepositoryProvider.get(), musicFileCacheProvider.get(), userRepositoryProvider.get());
  }

  public static PlayerViewModel_Factory create(Provider<MusicRepository> musicRepositoryProvider,
      Provider<MusicFileCache> musicFileCacheProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new PlayerViewModel_Factory(musicRepositoryProvider, musicFileCacheProvider, userRepositoryProvider);
  }

  public static PlayerViewModel newInstance(MusicRepository musicRepository,
      MusicFileCache musicFileCache, UserRepository userRepository) {
    return new PlayerViewModel(musicRepository, musicFileCache, userRepository);
  }
}
