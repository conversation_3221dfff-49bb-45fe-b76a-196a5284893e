package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.MusicRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<MusicRepository> musicRepositoryProvider;

  public MainViewModel_Factory(Provider<MusicRepository> musicRepositoryProvider) {
    this.musicRepositoryProvider = musicRepositoryProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(musicRepositoryProvider.get());
  }

  public static MainViewModel_Factory create(Provider<MusicRepository> musicRepositoryProvider) {
    return new MainViewModel_Factory(musicRepositoryProvider);
  }

  public static MainViewModel newInstance(MusicRepository musicRepository) {
    return new MainViewModel(musicRepository);
  }
}
