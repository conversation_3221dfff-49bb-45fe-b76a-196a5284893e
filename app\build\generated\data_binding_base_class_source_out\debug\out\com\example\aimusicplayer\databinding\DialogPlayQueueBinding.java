// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPlayQueueBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView btnClear;

  @NonNull
  public final TextView btnShuffle;

  @NonNull
  public final ImageView ivClose;

  @NonNull
  public final ImageView ivPlayMode;

  @NonNull
  public final LinearLayout llEmptyState;

  @NonNull
  public final LinearLayout llPlayMode;

  @NonNull
  public final RecyclerView rvPlayQueue;

  @NonNull
  public final TextView tvPlayMode;

  @NonNull
  public final TextView tvSongCount;

  private DialogPlayQueueBinding(@NonNull LinearLayout rootView, @NonNull TextView btnClear,
      @NonNull TextView btnShuffle, @NonNull ImageView ivClose, @NonNull ImageView ivPlayMode,
      @NonNull LinearLayout llEmptyState, @NonNull LinearLayout llPlayMode,
      @NonNull RecyclerView rvPlayQueue, @NonNull TextView tvPlayMode,
      @NonNull TextView tvSongCount) {
    this.rootView = rootView;
    this.btnClear = btnClear;
    this.btnShuffle = btnShuffle;
    this.ivClose = ivClose;
    this.ivPlayMode = ivPlayMode;
    this.llEmptyState = llEmptyState;
    this.llPlayMode = llPlayMode;
    this.rvPlayQueue = rvPlayQueue;
    this.tvPlayMode = tvPlayMode;
    this.tvSongCount = tvSongCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPlayQueueBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPlayQueueBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_play_queue, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPlayQueueBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_clear;
      TextView btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.btn_shuffle;
      TextView btnShuffle = ViewBindings.findChildViewById(rootView, id);
      if (btnShuffle == null) {
        break missingId;
      }

      id = R.id.iv_close;
      ImageView ivClose = ViewBindings.findChildViewById(rootView, id);
      if (ivClose == null) {
        break missingId;
      }

      id = R.id.iv_play_mode;
      ImageView ivPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (ivPlayMode == null) {
        break missingId;
      }

      id = R.id.ll_empty_state;
      LinearLayout llEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (llEmptyState == null) {
        break missingId;
      }

      id = R.id.ll_play_mode;
      LinearLayout llPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (llPlayMode == null) {
        break missingId;
      }

      id = R.id.rv_play_queue;
      RecyclerView rvPlayQueue = ViewBindings.findChildViewById(rootView, id);
      if (rvPlayQueue == null) {
        break missingId;
      }

      id = R.id.tv_play_mode;
      TextView tvPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (tvPlayMode == null) {
        break missingId;
      }

      id = R.id.tv_song_count;
      TextView tvSongCount = ViewBindings.findChildViewById(rootView, id);
      if (tvSongCount == null) {
        break missingId;
      }

      return new DialogPlayQueueBinding((LinearLayout) rootView, btnClear, btnShuffle, ivClose,
          ivPlayMode, llEmptyState, llPlayMode, rvPlayQueue, tvPlayMode, tvSongCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
