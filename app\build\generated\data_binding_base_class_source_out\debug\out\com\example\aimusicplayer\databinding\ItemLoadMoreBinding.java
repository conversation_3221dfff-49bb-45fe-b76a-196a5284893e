// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLoadMoreBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ProgressBar progressBarLoading;

  @NonNull
  public final TextView textLoadMore;

  private ItemLoadMoreBinding(@NonNull LinearLayout rootView,
      @NonNull ProgressBar progressBarLoading, @NonNull TextView textLoadMore) {
    this.rootView = rootView;
    this.progressBarLoading = progressBarLoading;
    this.textLoadMore = textLoadMore;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLoadMoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLoadMoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_load_more, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLoadMoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.progress_bar_loading;
      ProgressBar progressBarLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressBarLoading == null) {
        break missingId;
      }

      id = R.id.text_load_more;
      TextView textLoadMore = ViewBindings.findChildViewById(rootView, id);
      if (textLoadMore == null) {
        break missingId;
      }

      return new ItemLoadMoreBinding((LinearLayout) rootView, progressBarLoading, textLoadMore);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
