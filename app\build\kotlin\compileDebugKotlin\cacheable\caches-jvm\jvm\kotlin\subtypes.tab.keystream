androidx.navigation.NavArgs!androidx.navigation.NavDirections/com.example.aimusicplayer.data.model.SearchItemandroid.os.Parcelable1com.example.aimusicplayer.data.model.BaseResponse8com.example.aimusicplayer.data.repository.BaseRepositoryokhttp3.Interceptor*com.example.aimusicplayer.service.PlayMode+com.example.aimusicplayer.service.PlayState(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroidx.fragment.app.Fragmentandroid.view.View.androidx.recyclerview.widget.DiffUtil.Callbackjava.io.Serializable-com.example.aimusicplayer.utils.NetworkResultkotlin.Enumandroidx.lifecycle.ViewModel androidx.viewbinding.ViewBinding*com.bumptech.glide.GeneratedAppGlideModule%androidx.multidex.MultiDexApplicationandroidx.room.RoomDatabase-androidx.media3.datasource.DataSource.Factory2com.example.aimusicplayer.service.PlayerController+androidx.media3.session.MediaSessionService-com.example.aimusicplayer.service.UserService1androidx.recyclerview.widget.RecyclerView.Adapter$androidx.fragment.app.DialogFragment(androidx.appcompat.app.AppCompatActivity0androidx.viewpager2.adapter.FragmentStateAdapterandroid.widget.FrameLayout(com.bumptech.glide.module.AppGlideModule<com.bumptech.glide.load.resource.bitmap.BitmapTransformationdagger.internal.Factory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       