// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCacheManagementBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonBatchDelete;

  @NonNull
  public final MaterialButton buttonCacheSettings;

  @NonNull
  public final MaterialButton buttonClearAll;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ProgressBar progressBarCacheUsage;

  @NonNull
  public final RecyclerView recyclerViewCachedSongs;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final SwitchMaterial switchAutoCache;

  @NonNull
  public final SwitchMaterial switchWifiOnly;

  @NonNull
  public final TextView textCacheCount;

  @NonNull
  public final TextView textCacheSize;

  @NonNull
  public final TextView textCacheUsage;

  @NonNull
  public final TextView textEmptyState;

  @NonNull
  public final TextView textMaxCacheSize;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentCacheManagementBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonBatchDelete, @NonNull MaterialButton buttonCacheSettings,
      @NonNull MaterialButton buttonClearAll, @NonNull ProgressBar progressBar,
      @NonNull ProgressBar progressBarCacheUsage, @NonNull RecyclerView recyclerViewCachedSongs,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull SwitchMaterial switchAutoCache,
      @NonNull SwitchMaterial switchWifiOnly, @NonNull TextView textCacheCount,
      @NonNull TextView textCacheSize, @NonNull TextView textCacheUsage,
      @NonNull TextView textEmptyState, @NonNull TextView textMaxCacheSize,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.buttonBatchDelete = buttonBatchDelete;
    this.buttonCacheSettings = buttonCacheSettings;
    this.buttonClearAll = buttonClearAll;
    this.progressBar = progressBar;
    this.progressBarCacheUsage = progressBarCacheUsage;
    this.recyclerViewCachedSongs = recyclerViewCachedSongs;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.switchAutoCache = switchAutoCache;
    this.switchWifiOnly = switchWifiOnly;
    this.textCacheCount = textCacheCount;
    this.textCacheSize = textCacheSize;
    this.textCacheUsage = textCacheUsage;
    this.textEmptyState = textEmptyState;
    this.textMaxCacheSize = textMaxCacheSize;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCacheManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCacheManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_cache_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCacheManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonBatchDelete;
      MaterialButton buttonBatchDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonBatchDelete == null) {
        break missingId;
      }

      id = R.id.buttonCacheSettings;
      MaterialButton buttonCacheSettings = ViewBindings.findChildViewById(rootView, id);
      if (buttonCacheSettings == null) {
        break missingId;
      }

      id = R.id.buttonClearAll;
      MaterialButton buttonClearAll = ViewBindings.findChildViewById(rootView, id);
      if (buttonClearAll == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progressBarCacheUsage;
      ProgressBar progressBarCacheUsage = ViewBindings.findChildViewById(rootView, id);
      if (progressBarCacheUsage == null) {
        break missingId;
      }

      id = R.id.recyclerViewCachedSongs;
      RecyclerView recyclerViewCachedSongs = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewCachedSongs == null) {
        break missingId;
      }

      id = R.id.swipeRefreshLayout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.switchAutoCache;
      SwitchMaterial switchAutoCache = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoCache == null) {
        break missingId;
      }

      id = R.id.switchWifiOnly;
      SwitchMaterial switchWifiOnly = ViewBindings.findChildViewById(rootView, id);
      if (switchWifiOnly == null) {
        break missingId;
      }

      id = R.id.textCacheCount;
      TextView textCacheCount = ViewBindings.findChildViewById(rootView, id);
      if (textCacheCount == null) {
        break missingId;
      }

      id = R.id.textCacheSize;
      TextView textCacheSize = ViewBindings.findChildViewById(rootView, id);
      if (textCacheSize == null) {
        break missingId;
      }

      id = R.id.textCacheUsage;
      TextView textCacheUsage = ViewBindings.findChildViewById(rootView, id);
      if (textCacheUsage == null) {
        break missingId;
      }

      id = R.id.textEmptyState;
      TextView textEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (textEmptyState == null) {
        break missingId;
      }

      id = R.id.textMaxCacheSize;
      TextView textMaxCacheSize = ViewBindings.findChildViewById(rootView, id);
      if (textMaxCacheSize == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentCacheManagementBinding((CoordinatorLayout) rootView, buttonBatchDelete,
          buttonCacheSettings, buttonClearAll, progressBar, progressBarCacheUsage,
          recyclerViewCachedSongs, swipeRefreshLayout, switchAutoCache, switchWifiOnly,
          textCacheCount, textCacheSize, textCacheUsage, textEmptyState, textMaxCacheSize, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
