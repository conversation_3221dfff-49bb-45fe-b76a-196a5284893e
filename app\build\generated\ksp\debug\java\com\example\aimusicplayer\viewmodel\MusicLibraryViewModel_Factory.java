package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.repository.MusicRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicLibraryViewModel_Factory implements Factory<MusicLibraryViewModel> {
  private final Provider<MusicRepository> musicRepositoryProvider;

  public MusicLibraryViewModel_Factory(Provider<MusicRepository> musicRepositoryProvider) {
    this.musicRepositoryProvider = musicRepositoryProvider;
  }

  @Override
  public MusicLibraryViewModel get() {
    return newInstance(musicRepositoryProvider.get());
  }

  public static MusicLibraryViewModel_Factory create(
      Provider<MusicRepository> musicRepositoryProvider) {
    return new MusicLibraryViewModel_Factory(musicRepositoryProvider);
  }

  public static MusicLibraryViewModel newInstance(MusicRepository musicRepository) {
    return new MusicLibraryViewModel(musicRepository);
  }
}
