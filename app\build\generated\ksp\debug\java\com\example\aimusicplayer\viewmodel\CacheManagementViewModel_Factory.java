package com.example.aimusicplayer.viewmodel;

import com.example.aimusicplayer.data.cache.MusicFileCache;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CacheManagementViewModel_Factory implements Factory<CacheManagementViewModel> {
  private final Provider<MusicFileCache> musicFileCacheProvider;

  public CacheManagementViewModel_Factory(Provider<MusicFileCache> musicFileCacheProvider) {
    this.musicFileCacheProvider = musicFileCacheProvider;
  }

  @Override
  public CacheManagementViewModel get() {
    return newInstance(musicFileCacheProvider.get());
  }

  public static CacheManagementViewModel_Factory create(
      Provider<MusicFileCache> musicFileCacheProvider) {
    return new CacheManagementViewModel_Factory(musicFileCacheProvider);
  }

  public static CacheManagementViewModel newInstance(MusicFileCache musicFileCache) {
    return new CacheManagementViewModel(musicFileCache);
  }
}
