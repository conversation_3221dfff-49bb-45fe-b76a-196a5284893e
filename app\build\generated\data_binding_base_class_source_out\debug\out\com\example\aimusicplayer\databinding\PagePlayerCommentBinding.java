// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PagePlayerCommentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBackToLyric;

  @NonNull
  public final ImageButton btnSendComment;

  @NonNull
  public final EditText editTextComment;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewComments;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final TextView textEmptyComment;

  private PagePlayerCommentBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnBackToLyric, @NonNull ImageButton btnSendComment,
      @NonNull EditText editTextComment, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewComments, @NonNull SwipeRefreshLayout swipeRefreshLayout,
      @NonNull TextView textEmptyComment) {
    this.rootView = rootView;
    this.btnBackToLyric = btnBackToLyric;
    this.btnSendComment = btnSendComment;
    this.editTextComment = editTextComment;
    this.progressBar = progressBar;
    this.recyclerViewComments = recyclerViewComments;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.textEmptyComment = textEmptyComment;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PagePlayerCommentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PagePlayerCommentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.page_player_comment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PagePlayerCommentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back_to_lyric;
      ImageButton btnBackToLyric = ViewBindings.findChildViewById(rootView, id);
      if (btnBackToLyric == null) {
        break missingId;
      }

      id = R.id.btn_send_comment;
      ImageButton btnSendComment = ViewBindings.findChildViewById(rootView, id);
      if (btnSendComment == null) {
        break missingId;
      }

      id = R.id.edit_text_comment;
      EditText editTextComment = ViewBindings.findChildViewById(rootView, id);
      if (editTextComment == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_comments;
      RecyclerView recyclerViewComments = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewComments == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.text_empty_comment;
      TextView textEmptyComment = ViewBindings.findChildViewById(rootView, id);
      if (textEmptyComment == null) {
        break missingId;
      }

      return new PagePlayerCommentBinding((LinearLayout) rootView, btnBackToLyric, btnSendComment,
          editTextComment, progressBar, recyclerViewComments, swipeRefreshLayout, textEmptyComment);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
