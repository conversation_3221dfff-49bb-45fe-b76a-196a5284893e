package com.example.aimusicplayer.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchHistoryManager_Factory implements Factory<SearchHistoryManager> {
  private final Provider<Context> contextProvider;

  public SearchHistoryManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SearchHistoryManager get() {
    return newInstance(contextProvider.get());
  }

  public static SearchHistoryManager_Factory create(Provider<Context> contextProvider) {
    return new SearchHistoryManager_Factory(contextProvider);
  }

  public static SearchHistoryManager newInstance(Context context) {
    return new SearchHistoryManager(context);
  }
}
