// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCachedSongBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton buttonDelete;

  @NonNull
  public final CheckBox checkboxSelect;

  @NonNull
  public final TextView textCacheTime;

  @NonNull
  public final TextView textFileSize;

  @NonNull
  public final TextView textLastAccess;

  @NonNull
  public final TextView textSongName;

  private ItemCachedSongBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton buttonDelete, @NonNull CheckBox checkboxSelect,
      @NonNull TextView textCacheTime, @NonNull TextView textFileSize,
      @NonNull TextView textLastAccess, @NonNull TextView textSongName) {
    this.rootView = rootView;
    this.buttonDelete = buttonDelete;
    this.checkboxSelect = checkboxSelect;
    this.textCacheTime = textCacheTime;
    this.textFileSize = textFileSize;
    this.textLastAccess = textLastAccess;
    this.textSongName = textSongName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCachedSongBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCachedSongBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_cached_song, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCachedSongBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDelete;
      MaterialButton buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.checkboxSelect;
      CheckBox checkboxSelect = ViewBindings.findChildViewById(rootView, id);
      if (checkboxSelect == null) {
        break missingId;
      }

      id = R.id.textCacheTime;
      TextView textCacheTime = ViewBindings.findChildViewById(rootView, id);
      if (textCacheTime == null) {
        break missingId;
      }

      id = R.id.textFileSize;
      TextView textFileSize = ViewBindings.findChildViewById(rootView, id);
      if (textFileSize == null) {
        break missingId;
      }

      id = R.id.textLastAccess;
      TextView textLastAccess = ViewBindings.findChildViewById(rootView, id);
      if (textLastAccess == null) {
        break missingId;
      }

      id = R.id.textSongName;
      TextView textSongName = ViewBindings.findChildViewById(rootView, id);
      if (textSongName == null) {
        break missingId;
      }

      return new ItemCachedSongBinding((MaterialCardView) rootView, buttonDelete, checkboxSelect,
          textCacheTime, textFileSize, textLastAccess, textSongName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
