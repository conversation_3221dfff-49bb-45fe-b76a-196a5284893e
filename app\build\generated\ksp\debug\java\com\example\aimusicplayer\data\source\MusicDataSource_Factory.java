package com.example.aimusicplayer.data.source;

import android.content.Context;
import com.example.aimusicplayer.data.db.dao.PlaylistDao;
import com.example.aimusicplayer.data.db.dao.SongDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicDataSource_Factory implements Factory<MusicDataSource> {
  private final Provider<Context> contextProvider;

  private final Provider<OkHttpClient> okHttpClientProvider;

  private final Provider<SongDao> songDaoProvider;

  private final Provider<PlaylistDao> playlistDaoProvider;

  public MusicDataSource_Factory(Provider<Context> contextProvider,
      Provider<OkHttpClient> okHttpClientProvider, Provider<SongDao> songDaoProvider,
      Provider<PlaylistDao> playlistDaoProvider) {
    this.contextProvider = contextProvider;
    this.okHttpClientProvider = okHttpClientProvider;
    this.songDaoProvider = songDaoProvider;
    this.playlistDaoProvider = playlistDaoProvider;
  }

  @Override
  public MusicDataSource get() {
    return newInstance(contextProvider.get(), okHttpClientProvider.get(), songDaoProvider.get(), playlistDaoProvider.get());
  }

  public static MusicDataSource_Factory create(Provider<Context> contextProvider,
      Provider<OkHttpClient> okHttpClientProvider, Provider<SongDao> songDaoProvider,
      Provider<PlaylistDao> playlistDaoProvider) {
    return new MusicDataSource_Factory(contextProvider, okHttpClientProvider, songDaoProvider, playlistDaoProvider);
  }

  public static MusicDataSource newInstance(Context context, OkHttpClient okHttpClient,
      SongDao songDao, PlaylistDao playlistDao) {
    return new MusicDataSource(context, okHttpClient, songDao, playlistDao);
  }
}
