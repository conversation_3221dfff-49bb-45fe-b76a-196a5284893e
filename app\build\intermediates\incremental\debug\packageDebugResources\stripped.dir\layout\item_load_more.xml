<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center"
    android:padding="16dp"
    android:minHeight="56dp">

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progress_bar_loading"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:indeterminateTint="@color/colorPrimary"
        android:visibility="visible" />

    <!-- 加载文本 -->
    <TextView
        android:id="@+id/text_load_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="正在加载更多..."
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center" />

</LinearLayout>
