package com.example.aimusicplayer.ui.player

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.content.res.ColorStateList
import android.app.AlertDialog
import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable // 导入 Drawable
// import androidx.graphics.drawable.Drawable // Removed duplicate import
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.fragment.app.Fragment
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.navigation.fragment.findNavController
import androidx.palette.graphics.Palette
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.example.aimusicplayer.MusicApplication
import com.example.aimusicplayer.R
import com.example.aimusicplayer.ui.adapter.MediaItemAdapter
import com.example.aimusicplayer.ui.adapter.SearchResultsAdapter
import com.example.aimusicplayer.ui.adapter.SearchSuggestionsAdapter
import com.example.aimusicplayer.ui.dialog.PlayQueueDialogFragment
import com.example.aimusicplayer.databinding.FragmentPlayerBinding
// 删除不再需要的LyricAdapter和EmptyLyricAdapter导入
import com.example.aimusicplayer.service.PlayMode
import com.example.aimusicplayer.service.PlayState
import com.example.aimusicplayer.utils.AlbumArtCache
import com.example.aimusicplayer.utils.AnimationUtils

import com.example.aimusicplayer.utils.ImageUtils
import com.example.aimusicplayer.utils.PerformanceMonitor
import com.example.aimusicplayer.utils.TimeUtils
import com.example.aimusicplayer.network.NetworkStateManager
import com.example.aimusicplayer.data.model.LyricInfo
import com.example.aimusicplayer.data.model.LyricLine
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.regex.Pattern
import javax.inject.Inject
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager

/**
 * 播放器Fragment
 * 使用MVVM架构
 */
@AndroidEntryPoint
class PlayerFragment : Fragment() {

    private var _binding: FragmentPlayerBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PlayerViewModel by viewModels()

    @Inject
    lateinit var albumArtCache: AlbumArtCache

    @Inject
    lateinit var networkStateManager: NetworkStateManager

    @Inject
    lateinit var userService: com.example.aimusicplayer.service.UserService

    // 状态变量
    private var isSeekBarDragging = false
    private var isLyricMode = false
    private var backgroundColorAnimator: android.animation.ValueAnimator? = null

    // 专辑旋转动画
    private var albumRotationAnimator: ObjectAnimator? = null
    private var currentRotation = 0f

    // 歌曲切换动画
    private var songTransitionAnimator: ValueAnimator? = null
    private var previousSongId: Long = -1

    // 心动模式动画
    private var heartModeLoadingAnimator: AnimatorSet? = null
    private var heartModeHeartbeatAnimator: AnimatorSet? = null

    // 歌词拖动相关变量
    private var lyricDragging = false
    private var lyricDragStartY = 0f
    private var lyricDragStartTime = 0L

    // 搜索相关变量
    private lateinit var searchSuggestionsAdapter: SearchSuggestionsAdapter
    private lateinit var searchResultsAdapter: SearchResultsAdapter
    private var isSearchExpanded = false
    private var searchDebounceJob: Job? = null // 防抖动任务

    // 动画相关常量
    companion object {
        private const val BACKGROUND_ANIMATION_DURATION = 800L
        private const val COVER_ANIMATION_DURATION = 500L
        private const val SONG_TRANSITION_DURATION = 600L
        private const val ALBUM_ROTATION_DURATION = 20000L // 20秒旋转一圈
        private const val TAG = "PlayerFragment"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentPlayerBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 简化为两阶段初始化，提高稳定性
        initializeUIComponents()
    }

    /**
     * 第一阶段：UI组件初始化（同步）
     */
    private fun initializeUIComponents() {
        Log.d(TAG, "开始UI组件初始化")

        try {
            // 基础UI设置
            setupBasicUI()

            // ViewPager2设置
            setupViewPager()

            // 复杂交互设置
            setupComplexInteractions()

            // 异步初始化服务连接和观察者
            lifecycleScope.launch {
                delay(50) // 短暂延迟确保UI渲染完成
                initializeServicesAndObservers()
            }
        } catch (e: Exception) {
            Log.e(TAG, "UI组件初始化失败", e)
            // 降级处理：仅设置基础UI
            try {
                setupBasicUI()
                hideSidebarIfNeeded()
            } catch (fallbackError: Exception) {
                Log.e(TAG, "降级初始化也失败", fallbackError)
            }
        }
    }

    /**
     * 第二阶段：服务连接和观察者（异步）
     */
    private suspend fun initializeServicesAndObservers() {
        Log.d(TAG, "开始服务连接和观察者初始化")

        try {
            // 在主线程设置观察者
            withContext(Dispatchers.Main) {
                // 检查Fragment是否仍然有效且View已创建
                if (!isAdded || view == null || _binding == null) {
                    Log.w(TAG, "Fragment已销毁或View为null，跳过观察者设置")
                    return@withContext
                }

                // 确保ViewLifecycleOwner可用
                try {
                    val lifecycleOwner = viewLifecycleOwner
                    if (lifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.CREATED)) {
                        setupObservers()
                        hideSidebarIfNeeded()
                        Log.d(TAG, "观察者设置成功")
                    } else {
                        Log.w(TAG, "ViewLifecycleOwner状态不正确，跳过观察者设置")
                    }
                } catch (e: IllegalStateException) {
                    Log.w(TAG, "ViewLifecycleOwner不可用，延迟设置观察者", e)
                    // 延迟重试
                    delay(100)
                    if (isAdded && view != null && _binding != null) {
                        try {
                            setupObservers()
                            hideSidebarIfNeeded()
                            Log.d(TAG, "延迟观察者设置成功")
                        } catch (retryException: Exception) {
                            Log.e(TAG, "延迟观察者设置也失败", retryException)
                        }
                    }
                }
            }

            Log.d(TAG, "PlayerFragment初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "服务连接和观察者初始化失败", e)
        }
    }

    /**
     * 设置基础UI组件（第一阶段）
     */
    private fun setupBasicUI() {
        // 设置播放/暂停按钮点击事件 - 优化响应时间
        binding.buttonPlayerPlayPause.setOnClickListener {
            val startTime = System.currentTimeMillis()
            addButtonClickEffect(it)

            // 立即更新UI状态，提供即时反馈
            val currentState = viewModel.playState.value
            val isPlaying = currentState == PlayerViewModel.PlayState.PLAYING
            updatePlayPauseButton(!isPlaying) // 立即切换UI状态

            viewModel.togglePlayPause()

            // 监控响应时间
            val responseTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "播放/暂停按钮响应时间: ${responseTime}ms")
            if (responseTime > 200) {
                Log.w(TAG, "播放控制响应时间警告: ${responseTime}ms > 200ms")
            }
        }

        // 设置上一首按钮点击事件 - 优化响应时间
        binding.buttonPlayerPrev.setOnClickListener {
            val startTime = System.currentTimeMillis()
            addButtonClickEffect(it)

            // 立即提供视觉反馈
            it.animate().scaleX(0.9f).scaleY(0.9f).setDuration(100).withEndAction {
                it.animate().scaleX(1f).scaleY(1f).setDuration(100).start()
            }.start()

            viewModel.skipToPrevious()

            val responseTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "上一首按钮响应时间: ${responseTime}ms")
        }

        // 设置下一首按钮点击事件 - 优化响应时间
        binding.buttonPlayerNext.setOnClickListener {
            val startTime = System.currentTimeMillis()
            addButtonClickEffect(it)

            // 立即提供视觉反馈
            it.animate().scaleX(0.9f).scaleY(0.9f).setDuration(100).withEndAction {
                it.animate().scaleX(1f).scaleY(1f).setDuration(100).start()
            }.start()

            viewModel.skipToNext()

            val responseTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "下一首按钮响应时间: ${responseTime}ms")
        }

        // 设置播放模式按钮点击事件 - 优化响应时间
        binding.buttonPlayerPlayMode.setOnClickListener {
            val startTime = System.currentTimeMillis()
            addButtonClickEffect(it)

            // 立即更新播放模式图标
            val currentMode = viewModel.playMode.value
            val nextMode = when (currentMode) {
                PlayerViewModel.PlayMode.LOOP -> PlayerViewModel.PlayMode.SHUFFLE
                PlayerViewModel.PlayMode.SHUFFLE -> PlayerViewModel.PlayMode.SINGLE
                PlayerViewModel.PlayMode.SINGLE -> PlayerViewModel.PlayMode.LOOP
            }
            updatePlayModeButton(convertToServicePlayMode(nextMode)) // 立即更新UI

            viewModel.togglePlayMode()

            val responseTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "播放模式按钮响应时间: ${responseTime}ms")
        }

        // 设置收藏按钮点击事件
        binding.buttonPlayerCollect.setOnClickListener {
            addButtonClickEffect(it)
            // 检查登录状态
            if (!checkLoginStatusForFeature("收藏功能")) {
                return@setOnClickListener
            }
            toggleCollect()
        }

        // 设置播放列表按钮点击事件 - 切换到播放列表页面
        binding.buttonPlayerPlaylist.setOnClickListener {
            addButtonClickEffect(it)
            switchToPlaylistPage()
        }

        // 基础进度条设置
        binding.seekbarPlayerProgress.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    binding.textviewPlayerCurrentTime.text = TimeUtils.formatTime(progress.toLong())
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = false
                seekBar?.progress?.let { progress ->
                    viewModel.seekTo(progress.toLong())
                }
            }
        })

        // 初始化搜索功能
        setupSearchFunction()

        Log.d(TAG, "基础UI设置完成")
    }

    /**
     * 设置复杂交互组件（在第二阶段调用）
     */
    private fun setupComplexInteractions() {
        try {
            // 专辑封面和歌词切换
            binding.albumArt.setOnClickListener {
                toggleLyricMode()
            }

            binding.viewPagerPlayer.setOnClickListener {
                toggleLyricMode()
            }

            // 设置歌词点击跳转
            setupLyricInteraction()

            Log.d(TAG, "复杂交互设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置复杂交互失败", e)
        }
    }

    /**
     * 设置ViewPager2适配器
     */
    private fun setupViewPager() {
        try {
            // 创建适配器，支持歌词、评论、播放列表三个页面
            val adapter = PlayerPagerAdapter(this)
            binding.viewPagerPlayer.adapter = adapter

            // 设置TabLayout与ViewPager2关联（已隐藏TabLayout）
            val tabLayout = binding.tabLayoutPlayer
            val mediator = com.google.android.material.tabs.TabLayoutMediator(
                tabLayout, binding.viewPagerPlayer
            ) { tab, position ->
                when (position) {
                    PlayerPagerAdapter.PAGE_LYRIC -> {
                        tab.text = "歌词"
                        tab.setIcon(com.example.aimusicplayer.R.drawable.ic_lyrics)
                    }
                    PlayerPagerAdapter.PAGE_COMMENT -> {
                        tab.text = "评论"
                        tab.setIcon(com.example.aimusicplayer.R.drawable.ic_comment)
                    }
                    PlayerPagerAdapter.PAGE_PLAYLIST -> {
                        tab.text = "列表"
                        tab.setIcon(com.example.aimusicplayer.R.drawable.ic_playlist)
                    }
                }
            }
            mediator.attach()

            // 禁用手动滑动，只允许通过按钮切换页面
            binding.viewPagerPlayer.isUserInputEnabled = false

            // 设置默认显示歌词页面
            binding.viewPagerPlayer.currentItem = PlayerPagerAdapter.PAGE_LYRIC

            Log.d(TAG, "ViewPager2适配器和TabLayout设置成功")
        } catch (e: Exception) {
            Log.e(TAG, "设置ViewPager2适配器失败", e)
        }
    }

    /**
     * 隐藏侧边栏（如果需要）
     */
    private fun hideSidebarIfNeeded() {
        try {
            // 获取MainActivity的SidebarController
            val activity = requireActivity()
            if (activity is com.example.aimusicplayer.ui.main.MainActivity) {
                // 通过反射或其他方式获取SidebarController并隐藏侧边栏
                // 这里暂时使用简单的方法
                Log.d(TAG, "确保侧边栏隐藏")
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏侧边栏失败", e)
        }
    }

    /**
     * 添加按钮点击效果 - 使用优化的动画工具
     */
    private fun addButtonClickEffect(view: View) {
        // 使用新的动画工具提供按钮反馈
        AnimationUtils.buttonClickFeedback(view, 0.9f, 120)

        // 添加触觉反馈
        addHapticFeedback()
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察当前歌曲
        lifecycleScope.launch {
            viewModel.currentSong.collect { song ->
                song?.let {
                    // 获取歌曲ID
                    val songId = it.id

                    // 检查是否是新歌曲
                    val isSongChanged = previousSongId != songId

                    // 如果是新歌曲，添加切换动画
                    if (isSongChanged && previousSongId != -1L) {
                        playSongTransitionAnimation()
                    }

                    // 更新歌曲信息 - 简化默认显示
                    val title = it.name
                    val artist = it.artist

                    binding.songTitle.text = if (title.isNullOrBlank()) {
                        "暂无"
                    } else {
                        title
                    }

                    binding.songArtist.text = if (artist.isNullOrBlank()) {
                        "暂无"
                    } else {
                        artist
                    }

                    // 启动跑马灯效果（如果文本过长）
                    startMarqueeIfNeeded(binding.songTitle)
                    startMarqueeIfNeeded(binding.songArtist)

                    // 加载专辑封面 - 严格优先级策略：歌曲封面 > 专辑封面 > 默认封面
                    lifecycleScope.launch {
                        loadAlbumCoverWithPriority(songId, isSongChanged)
                    }

                    // 加载歌词
                    viewModel.loadLyricInfo(songId)

                    // 更新收藏状态
                    updateCollectButton(viewModel.isCurrentSongCollected.value)

                    // 保存当前歌曲ID
                    previousSongId = songId
                }
            }
        }

        // 观察播放状态
        lifecycleScope.launch {
            viewModel.playState.collect { state ->
                when (state) {
                    PlayerViewModel.PlayState.PLAYING -> {
                        updatePlayPauseButton(true)
                        startAlbumRotation()
                        // 启动黑胶唱片播放动画
                        binding.vinylRecordView.setPlaying(true)
                    }
                    PlayerViewModel.PlayState.PAUSED -> {
                        updatePlayPauseButton(false)
                        pauseAlbumRotation()
                        // 暂停黑胶唱片播放动画
                        binding.vinylRecordView.setPlaying(false)
                    }
                    PlayerViewModel.PlayState.IDLE -> {
                        updatePlayPauseButton(false)
                        resetAlbumRotation()
                        // 停止黑胶唱片播放动画
                        binding.vinylRecordView.setPlaying(false)
                    }
                    else -> {
                        // 其他状态
                    }
                }
            }
        }

        // 观察播放进度
        lifecycleScope.launch {
            viewModel.playProgress.collect { progress ->
                if (!isSeekBarDragging) {
                    binding.seekbarPlayerProgress.progress = progress.currentPosition.toInt()
                    binding.textviewPlayerCurrentTime.text = TimeUtils.formatTime(progress.currentPosition)
                    binding.textviewPlayerTotalTime.text = TimeUtils.formatTime(progress.duration)
                    binding.seekbarPlayerProgress.max = progress.duration.toInt()

                    // 更新歌词位置
                    updateLyricPosition(progress.currentPosition)
                }
            }
        }

        // 观察播放模式
        lifecycleScope.launch {
            viewModel.playMode.collect { mode ->
                updatePlayModeButton(convertToServicePlayMode(mode))
            }
        }

        // 观察心动模式状态
        lifecycleScope.launch {
            viewModel.heartModeEnabled.collect { enabled ->
                updateHeartModeButton(enabled)
            }
        }

        // 观察错误消息（用于心动模式等功能的用户反馈）
        lifecycleScope.launch {
            viewModel.errorMessage.collect { message ->
                if (!message.isNullOrEmpty()) {
                    // 显示Toast提示
                    Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                    Log.d(TAG, "显示用户提示: $message")
                }
            }
        }

        // 观察歌词 - 增强版本，支持实时同步和滚动
        lifecycleScope.launch {
            viewModel.lyrics.collect { lyricLines ->
                if (lyricLines.isNotEmpty()) {
                    // 统一使用LyricView，删除适配器方式以提高效率
                    // LyricView在ViewPager2的页面中，需要通过ViewPager2获取
                    val lyricView = findLyricViewInViewPager()
                    if (lyricView != null) {
                        // 转换PlayerViewModel.LyricLine到data.model.LyricLine
                        val convertedLyrics = lyricLines.map { lyricLine ->
                            com.example.aimusicplayer.data.model.LyricLine(
                                lyricLine.time,
                                lyricLine.content,
                                lyricLine.translation,
                            )
                        }

                        // 设置歌词到LyricView
                        lyricView.setLyrics(convertedLyrics)

                        // 设置歌词点击监听器
                        lyricView.onLyricClickListener = { lyricLine ->
                            // 跳转到对应时间
                            viewModel.seekTo(lyricLine.time)
                        }

                        // 设置拖动监听器
                        lyricView.onDragPositionChangeListener = { lyricLine ->
                            // 实时更新播放位置
                            viewModel.seekTo(lyricLine.time)
                        }

                        // 更新当前播放位置对应的歌词
                        updateLyricPosition(viewModel.currentPosition.value)
                    } else {
                        Log.w(TAG, "LyricView未找到，无法显示歌词")
                    }
                } else {
                    // 没有歌词时显示默认歌词
                    val lyricView = findLyricViewInViewPager()
                    if (lyricView != null) {
                        val defaultLyrics = createDefaultLyrics()
                        lyricView.setLyrics(defaultLyrics)
                    } else {
                        Log.w(TAG, "LyricView未找到，无法显示默认歌词")
                    }
                }
            }
        }

        // 观察当前歌曲是否已收藏
        lifecycleScope.launch {
            viewModel.isCurrentSongCollected.collect { isCollected ->
                updateCollectButton(isCollected)
            }
        }

        // 观察错误弹窗
        lifecycleScope.launch {
            viewModel.errorMessage.collect { errorMessage: String? ->
                if (!errorMessage.isNullOrEmpty()) {
                    showErrorDialog("错误", errorMessage)
                }
            }
        }

        // 观察搜索建议 - 重构版本
        lifecycleScope.launch {
            viewModel.searchItems.collectLatest { searchItems ->
                val searchItemList = searchItems.map {
                    com.example.aimusicplayer.data.model.SearchItem.SuggestionItem(it)
                }
                searchSuggestionsAdapter.submitList(searchItemList)
                binding.searchSuggestionsRecycler.visibility = if (searchItems.isNotEmpty()) View.VISIBLE else View.GONE
            }
        }

        // 观察搜索结果
        lifecycleScope.launch {
            viewModel.searchResults.collectLatest { results ->
                searchResultsAdapter.submitList(results)
                binding.searchResultsRecycler.visibility = if (results.isNotEmpty()) View.VISIBLE else View.GONE

                // 如果有搜索结果，隐藏ViewPager
                if (results.isNotEmpty()) {
                    binding.viewPagerPlayer.visibility = View.GONE
                } else {
                    binding.viewPagerPlayer.visibility = View.VISIBLE
                }
            }
        }

        // 观察搜索状态
        lifecycleScope.launch {
            viewModel.isSearching.collectLatest { isSearching ->
                // 可以在这里显示搜索加载状态
                Log.d(TAG, "搜索状态: $isSearching")
            }
        }

        // 观察网络状态变化
        lifecycleScope.launch {
            networkStateManager.networkState.collectLatest { networkState ->
                handleNetworkStateChange(networkState)
            }
        }

        // 观察请求策略变化
        lifecycleScope.launch {
            networkStateManager.requestStrategy.collectLatest { strategy ->
                handleRequestStrategyChange(strategy)
            }
        }
    }

    /**
     * 按优先级加载专辑封面
     * 优先级：1.歌曲封面(Song.picUrl/cover) > 2.专辑封面(Album.picUrl/MediaMetadata) > 3.默认封面
     */
    private suspend fun loadAlbumCoverWithPriority(songId: Long, withAnimation: Boolean = false) {
        try {
            Log.d(TAG, "开始按优先级加载专辑封面，歌曲ID: $songId")

            // 第一优先级：尝试获取歌曲封面
            val songCoverResult = tryLoadSongCover(songId)
            if (songCoverResult.success) {
                Log.i(TAG, "✅ 使用歌曲封面: ${songCoverResult.source}")
                loadCoverFromUri(songCoverResult.uri!!, songId, "歌曲封面", withAnimation)
                return
            }

            // 第二优先级：尝试获取专辑封面
            val albumCoverResult = tryLoadAlbumCover(songId)
            if (albumCoverResult.success) {
                Log.i(TAG, "✅ 使用专辑封面: ${albumCoverResult.source}")
                loadCoverFromUri(albumCoverResult.uri!!, songId, "专辑封面", withAnimation)
                return
            }

            // 第三优先级：使用默认封面
            Log.i(TAG, "✅ 使用默认封面")
            showDefaultCover(withAnimation)
        } catch (e: Exception) {
            Log.e(TAG, "加载专辑封面失败", e)
            showDefaultCover(withAnimation)
        }
    }

    /**
     * 尝试加载歌曲封面（第一优先级）
     */
    private suspend fun tryLoadSongCover(songId: Long): CoverLoadResult {
        return try {
            Log.d(TAG, "🎵 尝试获取歌曲封面...")

            // 从API获取歌曲详情
            val songDetail = viewModel.getSongDetail(songId)

            // 使用Song模型的新方法获取歌曲封面
            val songCoverUrl = songDetail?.getSongCoverUrl()

            if (!songCoverUrl.isNullOrEmpty()) {
                Log.d(TAG, "找到歌曲封面: $songCoverUrl")
            } else {
                Log.d(TAG, "歌曲无直接封面URL")
            }

            if (!songCoverUrl.isNullOrEmpty()) {
                CoverLoadResult(true, Uri.parse(songCoverUrl), "Song.picUrl/cover")
            } else {
                CoverLoadResult(false, null, "歌曲无封面")
            }
        } catch (e: Exception) {
            Log.w(TAG, "获取歌曲封面失败", e)
            CoverLoadResult(false, null, "歌曲封面获取异常: ${e.message}")
        }
    }

    /**
     * 尝试加载专辑封面（第二优先级）
     */
    private suspend fun tryLoadAlbumCover(songId: Long): CoverLoadResult {
        return try {
            Log.d(TAG, "💿 尝试获取专辑封面...")

            // 1. 先尝试从API获取专辑封面
            val songDetail = viewModel.getSongDetail(songId)
            val albumCoverUrl = songDetail?.getAlbumOnlyCoverUrl()

            if (!albumCoverUrl.isNullOrEmpty()) {
                Log.d(TAG, "找到专辑封面: $albumCoverUrl")
                return CoverLoadResult(true, Uri.parse(albumCoverUrl), "Album.picUrl")
            }

            Log.d(TAG, "专辑无封面URL")
            CoverLoadResult(false, null, "专辑无封面")
        } catch (e: Exception) {
            Log.w(TAG, "获取专辑封面失败", e)
            CoverLoadResult(false, null, "专辑封面获取异常: ${e.message}")
        }
    }

    /**
     * 从URI加载封面
     */
    private suspend fun loadCoverFromUri(uri: Uri, songId: Long, source: String, withAnimation: Boolean) {
        try {
            val cacheKey = "${source.replace(" ", "_").lowercase()}_$songId"

            // 先检查缓存
            val cachedBitmap = albumArtCache.getAlbumArt(cacheKey)
            if (cachedBitmap != null) {
                Log.d(TAG, "✅ 使用缓存的$source: $cacheKey")
                withContext(Dispatchers.Main) {
                    updateAlbumArt(cachedBitmap, withAnimation)
                }
                return
            }

            // 缓存未命中，先显示默认封面，然后异步加载
            Log.d(TAG, "缓存未命中，先显示默认封面，异步加载$source")
            withContext(Dispatchers.Main) {
                showDefaultCover(withAnimation)
                loadAlbumArtWithEnhancedCache(uri, songId, source)
            }
        } catch (e: Exception) {
            Log.e(TAG, "从URI加载封面失败: $uri", e)
            withContext(Dispatchers.Main) {
                showDefaultCover(withAnimation)
            }
        }
    }

    /**
     * 封面加载结果
     */
    private data class CoverLoadResult(
        val success: Boolean,
        val uri: Uri?,
        val source: String,
    )

    /**
     * 显示默认封面
     * @param withAnimation 是否添加动画效果
     */
    private fun showDefaultCover(withAnimation: Boolean = false) {
        try {
            val defaultBitmap = BitmapFactory.decodeResource(resources, R.drawable.default_album_art)
            if (defaultBitmap != null) {
                // 设置到VinylRecordView
                binding.vinylRecordView.setAlbumCover(null) // 使用默认封面

                // 直接设置到AlbumCoverView，避免递归调用
                binding.albumCoverView.setCoverBitmap(defaultBitmap)

                // 设置到备用ImageView
                if (withAnimation) {
                    binding.albumArt.animate()
                        .alpha(0f)
                        .setDuration(COVER_ANIMATION_DURATION / 2)
                        .withEndAction {
                            binding.albumArt.setImageBitmap(defaultBitmap)
                            binding.albumArt.animate()
                                .alpha(1f)
                                .setDuration(COVER_ANIMATION_DURATION / 2)
                                .start()
                        }
                        .start()
                } else {
                    binding.albumArt.setImageBitmap(defaultBitmap)
                }

                Log.d(TAG, "显示默认专辑封面")
            } else {
                Log.e(TAG, "无法加载默认专辑封面")
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示默认封面失败", e)
        }
    }

    /**
     * 更新专辑封面 - 优化版本，增强错误处理
     * @param bitmap 专辑封面位图
     * @param withAnimation 是否添加动画效果
     * @param coverUrl 专辑封面URL（可选）
     */
    private fun updateAlbumArt(bitmap: Bitmap?, withAnimation: Boolean = false, coverUrl: String? = null) {
        try {
            // 检查bitmap是否有效
            if (bitmap == null || bitmap.isRecycled) {
                Log.w(TAG, "位图无效，使用默认封面")
                showDefaultCover(withAnimation)
                return
            }

            // 设置到VinylRecordView
            binding.vinylRecordView.setAlbumCover(coverUrl)

            // 设置到AlbumCoverView（备用）
            binding.albumCoverView.setCoverBitmap(bitmap)

            // 同时设置到备用ImageView（如果需要）
            if (withAnimation) {
                // 优化动画，减少重绘
                binding.albumArt.animate()
                    .alpha(0f)
                    .scaleX(0.9f)
                    .scaleY(0.9f)
                    .setDuration(COVER_ANIMATION_DURATION / 2)
                    .withEndAction {
                        try {
                            // 设置新封面
                            binding.albumArt.setImageBitmap(bitmap)

                            // 淡入新封面
                            binding.albumArt.animate()
                                .alpha(1f)
                                .scaleX(1f)
                                .scaleY(1f)
                                .setDuration(COVER_ANIMATION_DURATION / 2)
                                .start()
                        } catch (e: Exception) {
                            Log.e(TAG, "动画设置封面失败", e)
                        }
                    }
                    .start()
            } else {
                // 直接设置专辑封面
                binding.albumArt.setImageBitmap(bitmap)
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新专辑封面失败", e)
            // 显示用户友好的错误提示
            showErrorSnackbar("专辑封面加载失败")
        }

        // 检查bitmap是否为null
        bitmap?.let { validBitmap ->
            // 生成调色板并提取颜色
            Palette.from(validBitmap).generate { palette ->
                palette?.let {
                    // 获取主色调
                    val dominantColor = it.getDominantColor(0xFF333333.toInt())

                    // 获取鲜艳的颜色
                    val vibrantColor = it.getVibrantColor(dominantColor)

                    // 使用鲜艳的颜色或主色调
                    val finalColor = if (vibrantColor != dominantColor) vibrantColor else dominantColor

                    // 更新背景颜色
                    updateBackgroundColor(finalColor)

                    // 获取文本颜色
                    val textColor = if (ColorUtils.calculateLuminance(finalColor) > 0.5) {
                        Color.BLACK
                    } else {
                        Color.WHITE
                    }

                    // 更新文本颜色
                    updateTextColor(textColor)
                }
            }

            // 生成模糊背景
            lifecycleScope.launch {
                val blurredBitmap = withContext(Dispatchers.IO) {
                    ImageUtils.blurBitmap(requireContext(), validBitmap, 25)
                }
                updateBlurredBackground(blurredBitmap, withAnimation)
            }
        }
    }

    /**
     * 使用增强型缓存加载专辑封面
     */
    private fun loadAlbumArtWithEnhancedCache(artworkUri: android.net.Uri, songId: Long, source: String = "专辑封面") {
        // 生成缓存键，包含来源信息
        val cacheKey = "${source.replace(" ", "_").lowercase()}_$songId"
        Log.d(TAG, "开始加载$source，缓存键: $cacheKey")

        // 加载专辑封面到ImageView
        Glide.with(this)
            .asBitmap()
            .load(artworkUri)
            .apply(
                RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop(),
            )
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 设置专辑封面
                    binding.albumArt.setImageBitmap(resource)

                    // 提取颜色
                    extractColorFromBitmap(resource)

                    // 生成模糊背景
                    lifecycleScope.launch {
                        val blurredBitmap = withContext(Dispatchers.IO) {
                            ImageUtils.blurBitmap(requireContext(), resource, 25)
                        }
                        updateBlurredBackground(blurredBitmap)

                        // 缓存模糊背景
                        albumArtCache.putAlbumArt("blur_$cacheKey", blurredBitmap)
                    }

                    // 缓存专辑封面
                    lifecycleScope.launch(Dispatchers.IO) {
                        albumArtCache.putAlbumArt(cacheKey, resource)
                    }
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    Log.e(TAG, "加载专辑封面失败")
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // 不需要处理
                }
            })
    }

    /**
     * 从位图中提取颜色
     */
    private fun extractColorFromBitmap(bitmap: Bitmap) {
        lifecycleScope.launch(Dispatchers.Default) {
            // 使用Palette提取颜色
            val palette = Palette.from(bitmap).generate()

            // 获取主色调
            val dominantColor = palette.getDominantColor(0xFF333333.toInt())

            // 获取鲜艳的颜色
            val vibrantColor = palette.getVibrantColor(dominantColor)

            // 使用鲜艳的颜色或主色调
            val finalColor = if (vibrantColor != dominantColor) vibrantColor else dominantColor

            // 在主线程更新UI
            withContext(Dispatchers.Main) {
                // 更新背景颜色
                updateBackgroundColor(finalColor)

                // 获取文本颜色
                val textColor = if (ColorUtils.calculateLuminance(finalColor) > 0.5) {
                    Color.BLACK
                } else {
                    Color.WHITE
                }

                // 更新文本颜色
                updateTextColor(textColor)
            }
        }
    }

    /**
     * 设置歌词点击跳转
     */
    /**
     * 设置歌词交互 - 优化版本，统一使用LyricView
     */
    private fun setupLyricInteraction() {
        // 设置歌词点击事件 - 直接切换歌词/封面显示模式
        binding.viewPagerPlayer.setOnClickListener {
            toggleLyricMode()
        }

        // 歌词交互已经在LyricView中实现，这里不需要额外的触摸处理
        // LyricView自带点击跳转和拖动交互功能
    }

    /**
     * 转换PlayMode类型
     */
    private fun convertToServicePlayMode(mode: PlayerViewModel.PlayMode): PlayMode {
        return when (mode) {
            PlayerViewModel.PlayMode.LOOP -> PlayMode.Loop
            PlayerViewModel.PlayMode.SHUFFLE -> PlayMode.Shuffle
            PlayerViewModel.PlayMode.SINGLE -> PlayMode.Single
        }
    }

    /**
     * 创建默认歌词
     */
    private fun createDefaultLyrics(): List<LyricLine> {
        val currentSong = viewModel.currentSong.value
        val songTitle = currentSong?.name ?: "未知歌曲"
        val artist = currentSong?.artist ?: "未知艺术家"

        // 根据歌曲类型提供不同的提示文字
        val noLyricText = when {
            songTitle.contains("纯音乐", ignoreCase = true) ||
                songTitle.contains("轻音乐", ignoreCase = true) ||
                songTitle.contains("instrumental", ignoreCase = true) ||
                artist.contains("轻音乐", ignoreCase = true) ||
                artist.contains("纯音乐", ignoreCase = true) -> "♪ 纯音乐，请静心聆听 ♪"
            songTitle.contains("钢琴", ignoreCase = true) ||
                artist.contains("钢琴", ignoreCase = true) -> "♪ 钢琴曲，感受指尖的优雅 ♪"
            songTitle.contains("古典", ignoreCase = true) ||
                artist.contains("古典", ignoreCase = true) -> "♪ 古典音乐，品味永恒之美 ♪"
            songTitle.contains("爵士", ignoreCase = true) ||
                artist.contains("爵士", ignoreCase = true) -> "♫ 爵士乐，享受自由的节拍 ♫"
            else -> "暂无歌词"
        }

        return listOf(
            LyricLine(0, "♪ 正在播放 ♪", null),
            LyricLine(2000, songTitle, null),
            LyricLine(4000, "演唱：$artist", null),
            LyricLine(6000, "", null),
            LyricLine(8000, noLyricText, null),
            LyricLine(10000, "请放松心情，享受音乐时光", null),
            LyricLine(12000, "", null),
            LyricLine(14000, "♫ 让音乐洗涤心灵 ♫", null),
        )
    }

    /**
     * 解析歌词信息为LyricLine列表
     */
    private fun parseLyricInfoToLines(lyricInfo: Any): List<LyricLine> {
        return try {
            when (lyricInfo) {
                is String -> {
                    // 如果是字符串，使用简单的歌词解析
                    // 避免在非suspend函数中调用suspend函数
                    parseLrcString(lyricInfo)
                }
                is com.example.aimusicplayer.data.model.LyricInfo -> {
                    // 如果是LyricInfo对象，直接转换
                    lyricInfo.entries.map { kotlinLine ->
                        LyricLine(
                            time = kotlinLine.time,
                            text = kotlinLine.text,
                            translation = kotlinLine.translation,
                        )
                    }
                }
                else -> {
                    Log.w(TAG, "未知的歌词信息类型: ${lyricInfo.javaClass}")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析歌词失败", e)
            emptyList()
        }
    }

    /**
     * 解析LRC格式字符串（同步版本）
     */
    private fun parseLrcString(lrcContent: String): List<LyricLine> {
        val lyricLines = mutableListOf<LyricLine>()

        try {
            val lines = lrcContent.split("\n", "\r\n")
            val timePattern = Pattern.compile("\\[(\\d{2}):(\\d{2})\\.(\\d{2,3})\\]")

            for (line in lines) {
                val trimmedLine = line.trim()
                if (trimmedLine.isEmpty()) continue

                val matcher = timePattern.matcher(trimmedLine)
                val times = mutableListOf<Long>()

                // 提取所有时间标签
                while (matcher.find()) {
                    val minutes = matcher.group(1)?.toInt() ?: 0
                    val seconds = matcher.group(2)?.toInt() ?: 0
                    val milliseconds = matcher.group(3)?.let {
                        if (it.length == 2) it.toInt() * 10 else it.toInt()
                    } ?: 0

                    val timeMs = (minutes * 60 * 1000 + seconds * 1000 + milliseconds).toLong()
                    times.add(timeMs)
                }

                // 提取歌词文本
                val text = trimmedLine.replace(Regex("\\[\\d{2}:\\d{2}\\.\\d{2,3}\\]"), "").trim()

                // 为每个时间标签创建歌词行
                for (time in times) {
                    if (text.isNotEmpty()) {
                        lyricLines.add(LyricLine(time, text, null))
                    }
                }
            }

            // 按时间排序
            lyricLines.sortBy { it.time }
        } catch (e: Exception) {
            Log.e(TAG, "解析LRC字符串失败", e)
        }

        return lyricLines
    }

    /**
     * 更新歌词位置 - 优化版本，只支持LyricView
     */
    private fun updateLyricPosition(position: Long) {
        // 只使用LyricView，提高效率
        val lyricView = findLyricViewInViewPager()
        if (lyricView != null) {
            // 更新LyricView的当前行
            lyricView.updateCurrentLine(position, true)
        } else {
            Log.w(TAG, "LyricView未找到，无法更新歌词位置")
        }
    }

    /**
     * 在ViewPager2中查找LyricView
     */
    private fun findLyricViewInViewPager(): LyricView? {
        return try {
            // 尝试从ViewPager2中获取当前页面的LyricView
            val viewPager = binding.viewPagerPlayer
            val adapter = viewPager.adapter as? PlayerPagerAdapter

            if (adapter != null) {
                // 获取当前Fragment
                val currentFragment = adapter.getCurrentFragment()
                if (currentFragment is LyricPageFragment) {
                    return currentFragment.getLyricView()
                }
            }

            Log.w(TAG, "无法从ViewPager2中获取LyricView")
            null
        } catch (e: Exception) {
            Log.e(TAG, "查找LyricView失败", e)
            null
        }
    }

    /**
     * 更新背景颜色
     */
    private fun updateBackgroundColor(color: Int) {
        // 取消之前的动画
        backgroundColorAnimator?.cancel()

        // 获取当前背景颜色
        val currentColor = (binding.root.background as? ColorDrawable)?.color ?: Color.BLACK

        // 创建颜色过渡动画
        backgroundColorAnimator = android.animation.ValueAnimator.ofArgb(currentColor, color).apply {
            duration = BACKGROUND_ANIMATION_DURATION
            interpolator = android.view.animation.DecelerateInterpolator()
            addUpdateListener { animator ->
                val animatedColor = animator.animatedValue as Int
                binding.root.setBackgroundColor(animatedColor)
            }
            start()
        }
    }

    /**
     * 更新模糊背景 - 优化版本，支持平滑过渡
     * @param bitmap 模糊背景位图
     * @param withAnimation 是否添加动画效果
     */
    private fun updateBlurredBackground(bitmap: Bitmap?, withAnimation: Boolean = true) {
        if (bitmap == null) return

        if (withAnimation) {
            // 创建交叉淡入淡出效果
            // 先淡出当前背景
            binding.backgroundBlur.animate()
                .alpha(0f)
                .setDuration(COVER_ANIMATION_DURATION / 2)
                .setInterpolator(android.view.animation.AccelerateInterpolator())
                .withEndAction {
                    // 设置新背景
                    binding.backgroundBlur.setImageBitmap(bitmap)

                    // 淡入新背景，使用更平滑的插值器
                    binding.backgroundBlur.animate()
                        .alpha(0.8f)
                        .setDuration(COVER_ANIMATION_DURATION / 2)
                        .setInterpolator(android.view.animation.DecelerateInterpolator())
                        .start()
                }
                .start()
        } else {
            // 直接设置模糊背景
            binding.backgroundBlur.setImageBitmap(bitmap)
            binding.backgroundBlur.alpha = 0.8f
        }
    }

    /**
     * 播放歌曲切换动画 - 使用优化的动画工具
     */
    private fun playSongTransitionAnimation() {
        // 取消之前的动画
        songTransitionAnimator?.cancel()

        // 重置专辑封面旋转角度
        binding.albumCoverView.switchTrack()

        // 为专辑封面添加缩放动画
        AnimationUtils.scale(binding.albumCoverView, 1f, 0.9f, 200) {
            AnimationUtils.scale(binding.albumCoverView, 0.9f, 1f, 200)
        }

        // 为歌曲标题添加滑动动画
        AnimationUtils.slideOut(binding.songTitle, -50f, 200, false) {
            AnimationUtils.slideIn(binding.songTitle, 50f, 200)
        }

        // 为艺术家信息添加淡出淡入动画
        AnimationUtils.fadeOut(binding.songArtist, 200, false) {
            AnimationUtils.fadeIn(binding.songArtist, 200)
        }

        // 为播放控制按钮添加心跳动画
        AnimationUtils.heartbeat(binding.buttonPlayerPlayPause, 1.1f, 400)
    }

    /**
     * 开始专辑旋转动画
     */
    private fun startAlbumRotation() {
        // 使用AlbumCoverView的动画
        binding.albumCoverView.start()

        // 取消之前的动画
        albumRotationAnimator?.cancel()

        // 保存当前旋转角度
        currentRotation = binding.vinylBackground.rotation

        // 创建黑胶唱片旋转动画（备用）
        val vinylAnimator = ObjectAnimator.ofFloat(binding.vinylBackground, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 创建专辑封面旋转动画（备用）
        @Suppress("UNUSED_VARIABLE")
        val albumAnimator = ObjectAnimator.ofFloat(binding.albumArt, View.ROTATION, currentRotation, currentRotation + 360f).apply {
            duration = ALBUM_ROTATION_DURATION
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 保存动画引用
        albumRotationAnimator = vinylAnimator
    }

    /**
     * 暂停专辑旋转动画
     */
    private fun pauseAlbumRotation() {
        // 使用AlbumCoverView的暂停方法
        binding.albumCoverView.pause()

        // 保存当前旋转角度
        currentRotation = binding.vinylBackground.rotation

        // 暂停动画
        albumRotationAnimator?.pause()
    }

    /**
     * 重置专辑旋转动画
     */
    private fun resetAlbumRotation() {
        // 使用AlbumCoverView的重置方法
        binding.albumCoverView.reset()

        // 取消之前的动画
        albumRotationAnimator?.cancel()

        // 重置旋转角度
        binding.vinylBackground.rotation = 0f
        binding.albumArt.rotation = 0f
        currentRotation = 0f
    }

    /**
     * 切换歌词/封面显示模式
     */
    private fun toggleLyricMode() {
        isLyricMode = !isLyricMode

        if (isLyricMode) {
            // 切换到歌词模式
            binding.albumArt.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    binding.albumArt.visibility = View.GONE
                    binding.vinylBackground.visibility = View.GONE
                    binding.viewPagerPlayer.visibility = View.VISIBLE
                    binding.viewPagerPlayer.alpha = 0f
                    binding.viewPagerPlayer.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
                .start()
        } else {
            // 切换到封面模式
            binding.viewPagerPlayer.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction {
                    binding.viewPagerPlayer.visibility = View.GONE
                    binding.albumArt.visibility = View.VISIBLE
                    binding.vinylBackground.visibility = View.VISIBLE
                    binding.albumArt.alpha = 0f
                    binding.albumArt.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
                .start()
        }
    }

    /**
     * 更新文本颜色
     */
    private fun updateTextColor(color: Int) {
        // 更新文本颜色
        binding.songTitle.setTextColor(color)
        binding.songArtist.setTextColor(ColorUtils.setAlphaComponent(color, 200))
        binding.textviewPlayerCurrentTime.setTextColor(color)
        binding.textviewPlayerTotalTime.setTextColor(color)
    }

    /**
     * 更新收藏按钮状态
     */
    private fun updateCollectButton(isCollected: Boolean) {
        binding.buttonPlayerCollect.isSelected = isCollected
    }

    /**
     * 切换收藏状态 - 增强版本，支持动画和反馈
     */
    private fun toggleCollect() {
        // 获取当前歌曲
        val currentSong = viewModel.currentSong.value
        if (currentSong == null) {
            showSnackbar("没有正在播放的歌曲")
            return
        }

        // 获取当前收藏状态
        val isCurrentlyCollected = viewModel.isCurrentSongCollected.value

        // 添加收藏按钮动画
        animateCollectButton(!isCurrentlyCollected)

        // 切换收藏状态
        viewModel.toggleCollect()

        // 显示反馈信息
        val message = if (isCurrentlyCollected) {
            "已取消收藏「${currentSong.name}」"
        } else {
            "已收藏「${currentSong.name}」"
        }
        showSnackbar(message)

        // 添加触觉反馈
        addHapticFeedback()
    }

    /**
     * 收藏按钮动画 - 增强版本，支持心跳和颜色切换效果
     */
    private fun animateCollectButton(isCollected: Boolean) {
        val button = binding.buttonPlayerCollect

        // 第一阶段：放大动画
        button.animate()
            .scaleX(1.3f)
            .scaleY(1.3f)
            .setDuration(150)
            .setInterpolator(android.view.animation.OvershootInterpolator(2f))
            .withEndAction {
                // 更新按钮状态（切换图标颜色）
                button.isSelected = isCollected

                // 第二阶段：回弹动画
                button.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .setInterpolator(android.view.animation.BounceInterpolator())
                    .withEndAction {
                        // 第三阶段：轻微心跳效果
                        if (isCollected) {
                            button.animate()
                                .scaleX(1.1f)
                                .scaleY(1.1f)
                                .setDuration(100)
                                .withEndAction {
                                    button.animate()
                                        .scaleX(1f)
                                        .scaleY(1f)
                                        .setDuration(100)
                                        .start()
                                }
                                .start()
                        }
                    }
                    .start()
            }
            .start()
    }

    /**
     * 切换心动模式 - 增强版本，添加加载动画和推荐歌曲管理
     */
    private fun toggleHeartMode() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "切换心动模式")

        // 获取当前状态
        val currentState = viewModel.heartModeEnabled.value

        // 添加心动模式按钮动画
        animateHeartModeButton(!currentState)

        // 如果开启心动模式，显示加载动画
        if (!currentState) {
            showHeartModeLoadingAnimation()
        }

        // 切换心动模式状态
        viewModel.toggleHeartMode()

        // 显示反馈信息
        val message = if (currentState) {
            "心动模式已关闭"
        } else {
            "心动模式已开启，正在为您推荐相似歌曲"
        }
        showHeartModeToast(message)

        // 监控响应时间
        val responseTime = System.currentTimeMillis() - startTime
        Log.d(TAG, "心动模式按钮响应时间: ${responseTime}ms")
        if (responseTime > 200) {
            Log.w(TAG, "心动模式响应时间警告: ${responseTime}ms > 200ms")
        }

        // 添加触觉反馈
        addHapticFeedback()

        // 如果开启心动模式，启动推荐歌曲加载
        if (!currentState) {
            loadHeartModeRecommendations()
        }
    }

    /**
     * 心动模式按钮动画 - 爱心跳动效果
     */
    private fun animateHeartModeButton(isEnabled: Boolean) {
        val button = binding.heartModeButton

        // 第一阶段：放大动画
        button.animate()
            .scaleX(1.4f)
            .scaleY(1.4f)
            .setDuration(150)
            .setInterpolator(android.view.animation.OvershootInterpolator(3f))
            .withEndAction {
                // 更新按钮状态（切换图标和背景）
                button.isSelected = isEnabled

                // 第二阶段：回弹动画
                button.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .setInterpolator(android.view.animation.BounceInterpolator())
                    .withEndAction {
                        // 第三阶段：心跳效果（如果开启）
                        if (isEnabled) {
                            // 连续心跳动画
                            button.animate()
                                .scaleX(1.2f)
                                .scaleY(1.2f)
                                .setDuration(120)
                                .withEndAction {
                                    button.animate()
                                        .scaleX(1f)
                                        .scaleY(1f)
                                        .setDuration(120)
                                        .withEndAction {
                                            // 第二次心跳
                                            button.animate()
                                                .scaleX(1.15f)
                                                .scaleY(1.15f)
                                                .setDuration(100)
                                                .withEndAction {
                                                    button.animate()
                                                        .scaleX(1f)
                                                        .scaleY(1f)
                                                        .setDuration(100)
                                                        .start()
                                                }
                                                .start()
                                        }
                                        .start()
                                }
                                .start()
                        }
                    }
                    .start()
            }
            .start()
    }

    /**
     * 更新心动模式按钮状态 - 增强版本，添加视觉反馈
     */
    private fun updateHeartModeButton(isEnabled: Boolean) {
        binding.heartModeButton.isSelected = isEnabled

        // 添加颜色变化动画
        val colorFrom = if (isEnabled) {
            ContextCompat.getColor(requireContext(), R.color.heart_mode_inactive)
        } else {
            ContextCompat.getColor(requireContext(), R.color.heart_mode_active)
        }

        val colorTo = if (isEnabled) {
            ContextCompat.getColor(requireContext(), R.color.heart_mode_active)
        } else {
            ContextCompat.getColor(requireContext(), R.color.heart_mode_inactive)
        }

        // 颜色渐变动画
        ValueAnimator.ofArgb(colorFrom, colorTo).apply {
            duration = 300
            addUpdateListener { animator ->
                val color = animator.animatedValue as Int
                binding.heartModeButton.imageTintList = ColorStateList.valueOf(color)
            }
            start()
        }

        // 如果启用，添加持续的心跳效果
        if (isEnabled) {
            startHeartModeHeartbeat()
        } else {
            stopHeartModeHeartbeat()
        }
    }

    /**
     * 显示心动模式加载动画
     */
    private fun showHeartModeLoadingAnimation() {
        // 在心动模式按钮周围显示加载圆环
        val button = binding.heartModeButton

        // 创建加载圆环动画
        val rotationAnimator = ObjectAnimator.ofFloat(button, "rotation", 0f, 360f).apply {
            duration = 1000
            repeatCount = ObjectAnimator.INFINITE
            interpolator = LinearInterpolator()
        }

        // 创建缩放脉冲动画
        val scaleAnimator = ObjectAnimator.ofFloat(button, "scaleX", 1f, 1.1f, 1f).apply {
            duration = 800
            repeatCount = ObjectAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }

        val scaleYAnimator = ObjectAnimator.ofFloat(button, "scaleY", 1f, 1.1f, 1f).apply {
            duration = 800
            repeatCount = ObjectAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }

        // 组合动画
        heartModeLoadingAnimator = AnimatorSet().apply {
            playTogether(rotationAnimator, scaleAnimator, scaleYAnimator)
            start()
        }

        Log.d(TAG, "心动模式加载动画已启动")
    }

    /**
     * 隐藏心动模式加载动画
     */
    private fun hideHeartModeLoadingAnimation() {
        heartModeLoadingAnimator?.cancel()
        heartModeLoadingAnimator = null

        // 恢复按钮状态
        binding.heartModeButton.apply {
            rotation = 0f
            scaleX = 1f
            scaleY = 1f
        }

        Log.d(TAG, "心动模式加载动画已停止")
    }

    /**
     * 启动心动模式持续心跳效果
     */
    private fun startHeartModeHeartbeat() {
        stopHeartModeHeartbeat() // 先停止之前的动画

        val button = binding.heartModeButton

        // 创建心跳动画
        val heartbeatAnimator = ObjectAnimator.ofFloat(button, "scaleX", 1f, 1.05f, 1f).apply {
            duration = 1200
            repeatCount = ObjectAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }

        val heartbeatYAnimator = ObjectAnimator.ofFloat(button, "scaleY", 1f, 1.05f, 1f).apply {
            duration = 1200
            repeatCount = ObjectAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }

        heartModeHeartbeatAnimator = AnimatorSet().apply {
            playTogether(heartbeatAnimator, heartbeatYAnimator)
            start()
        }
    }

    /**
     * 停止心动模式持续心跳效果
     */
    private fun stopHeartModeHeartbeat() {
        heartModeHeartbeatAnimator?.cancel()
        heartModeHeartbeatAnimator = null
    }

    /**
     * 加载心动模式推荐歌曲
     */
    private fun loadHeartModeRecommendations() {
        val currentSong = viewModel.currentSong.value
        if (currentSong == null) {
            Log.w(TAG, "当前无歌曲，无法加载心动模式推荐")
            hideHeartModeLoadingAnimation()
            return
        }

        Log.d(TAG, "开始加载心动模式推荐歌曲，当前歌曲: ${currentSong.name}")

        // 使用PerformanceMonitor监控推荐加载性能
        val performanceTimer = PerformanceMonitor.createMonitor("心动模式推荐加载")

        lifecycleScope.launch {
            try {
                // 调用ViewModel加载推荐歌曲
                viewModel.loadHeartModeSongs(currentSong.id)

                // 监听推荐结果
                viewModel.heartModeRecommendations.collect { recommendations ->
                    if (recommendations.isNotEmpty()) {
                        Log.d(TAG, "心动模式推荐加载成功: ${recommendations.size}首歌曲")

                        // 停止加载动画
                        hideHeartModeLoadingAnimation()

                        // 显示推荐成功提示
                        showHeartModeToast("为您推荐了${recommendations.size}首相似歌曲")

                        // 性能监控
                        performanceTimer.endUIResponse()

                        // 可选：自动播放第一首推荐歌曲
                        // playFirstRecommendation(recommendations)

                        return@collect
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载心动模式推荐失败", e)
                hideHeartModeLoadingAnimation()
                showHeartModeToast("推荐歌曲加载失败，请稍后重试")

                // 关闭心动模式
                viewModel.toggleHeartMode()
            }
        }

        // 设置超时处理
        lifecycleScope.launch {
            delay(10000) // 10秒超时
            if (heartModeLoadingAnimator?.isRunning == true) {
                Log.w(TAG, "心动模式推荐加载超时")
                hideHeartModeLoadingAnimation()
                showHeartModeToast("推荐歌曲加载超时，请检查网络连接")
                viewModel.toggleHeartMode()
            }
        }
    }

    /**
     * 显示心动模式提示
     */
    private fun showHeartModeToast(message: String) {
        try {
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.theme_accent))
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.text_light))
                .show()
        } catch (e: Exception) {
            Log.e(TAG, "显示心动模式提示失败", e)
        }
    }

    /**
     * 通用按钮点击动画效果 - 替代波纹效果
     */
    private fun addButtonClickEffect(view: View, scaleRatio: Float = 0.9f) {
        // 取消之前的动画
        view.animate().cancel()

        // 按下效果：缩小
        view.animate()
            .scaleX(scaleRatio)
            .scaleY(scaleRatio)
            .setDuration(100)
            .setInterpolator(android.view.animation.AccelerateInterpolator())
            .withEndAction {
                // 释放效果：恢复并轻微放大
                view.animate()
                    .scaleX(1.05f)
                    .scaleY(1.05f)
                    .setDuration(100)
                    .setInterpolator(android.view.animation.DecelerateInterpolator())
                    .withEndAction {
                        // 最终恢复正常大小
                        view.animate()
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(100)
                            .setInterpolator(android.view.animation.AccelerateDecelerateInterpolator())
                            .start()
                    }
                    .start()
            }
            .start()

        // 添加触觉反馈
        addHapticFeedback()
    }

    /**
     * 添加触觉反馈
     */
    private fun addHapticFeedback() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                @Suppress("DEPRECATION")
                val vibrator = requireActivity().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                vibrator.vibrate(VibrationEffect.createOneShot(30, VibrationEffect.DEFAULT_AMPLITUDE))
            }
        } catch (e: Exception) {
            Log.e(TAG, "触觉反馈失败", e)
        }
    }

    /**
     * 检查登录状态，如果未登录则显示提示
     * @param featureName 功能名称，用于显示提示信息
     * @return 是否已登录
     */
    private fun checkLoginStatusForFeature(featureName: String): Boolean {
        return try {
            if (userService.isLogin()) {
                true
            } else {
                // 显示游客状态提示
                Toast.makeText(
                    requireContext(),
                    "请先登录后使用$featureName",
                    Toast.LENGTH_SHORT
                ).show()
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查登录状态失败", e)
            Toast.makeText(
                requireContext(),
                "登录状态检查失败，请稍后重试",
                Toast.LENGTH_SHORT
            ).show()
            false
        }
    }

    /**
     * 更新播放/暂停按钮状态，带流畅动画
     */
    private fun updatePlayPauseButton(isPlaying: Boolean) {
        val button = binding.buttonPlayerPlayPause

        // 如果状态没有变化，不执行动画
        if (button.isSelected == isPlaying) return

        // 使用更流畅的动画：缩放+旋转+弹性效果
        button.animate()
            .scaleX(0.85f)
            .scaleY(0.85f)
            .rotation(button.rotation + 90f)
            .setDuration(150)
            .setInterpolator(android.view.animation.AccelerateInterpolator())
            .withEndAction {
                // 更新按钮状态
                button.isSelected = isPlaying

                // 恢复缩放，继续旋转，添加弹性效果
                button.animate()
                    .scaleX(1.05f)
                    .scaleY(1.05f)
                    .rotation(button.rotation + 90f)
                    .setDuration(100)
                    .setInterpolator(android.view.animation.DecelerateInterpolator())
                    .withEndAction {
                        // 最终恢复到正常大小
                        button.animate()
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(50)
                            .setInterpolator(android.view.animation.AccelerateDecelerateInterpolator())
                            .start()
                    }
                    .start()
            }
            .start()
    }

    /**
     * 更新播放模式按钮
     */
    private fun updatePlayModeButton(mode: PlayMode) {
        when (mode) {
            PlayMode.Loop -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_repeat)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_loop)
            }
            PlayMode.Single -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_repeat_one)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_single)
            }
            PlayMode.Shuffle -> {
                binding.buttonPlayerPlayMode.setImageResource(R.drawable.ic_shuffle)
                binding.buttonPlayerPlayMode.contentDescription = getString(R.string.play_mode_shuffle)
            }
        }
    }

    /**
     * 显示播放队列对话框 (新版本，使用PlayQueueDialogFragment)
     */
    private fun showPlayQueueDialog() {
        try {
            // 更新播放队列数据
            viewModel.updatePlayQueue(emptyList())

            // 创建并显示播放队列对话框
            val dialog = PlayQueueDialogFragment.newInstance()
            dialog.show(parentFragmentManager, "PlayQueueDialog")

            Log.d(TAG, "播放队列对话框已显示")
        } catch (e: Exception) {
            Log.e(TAG, "显示播放队列对话框失败", e)
            showErrorSnackbar("无法显示播放列表")
        }
    }

    /**
     * 显示播放列表对话框 (旧版本，保留兼容性)
     * 增强版：添加动画效果
     */
    private fun showPlaylistDialog() {
        // 创建底部对话框
        val dialog = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val dialogView = layoutInflater.inflate(R.layout.dialog_playlist, null)
        dialog.setContentView(dialogView)

        // 设置动画效果
        setupDialogAnimation(dialog)

        // 获取RecyclerView
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_view_playlist)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // 获取播放列表数量文本
        val countTextView = dialogView.findViewById<TextView>(R.id.text_playlist_count)

        // 获取空播放列表提示
        val emptyView = dialogView.findViewById<TextView>(R.id.text_empty_playlist)

        // 获取操作按钮
        val clearButton = dialogView.findViewById<Button>(R.id.button_clear_playlist)
        val shuffleButton = dialogView.findViewById<Button>(R.id.button_shuffle_playlist)

        // 创建增强型适配器，支持删除和拖拽排序
        val adapter = MediaItemAdapter(emptyList()) { position ->
            // 点击播放列表项
            viewModel.playAtIndex(position)
            dialog.dismiss()
        }

        // 设置长按删除功能
        adapter.setOnItemLongClickListener { position, mediaItem ->
            showDeleteSongDialog(mediaItem, position) {
                // 删除成功后刷新列表
                adapter.notifyItemRemoved(position)
            }
        }

        recyclerView.adapter = adapter

        // 观察播放列表 - 使用StateFlow版本
        lifecycleScope.launch {
            viewModel.playQueueFlow.collectLatest { mediaItems ->
                if (mediaItems.isNotEmpty()) {
                    // 更新播放列表数量
                    countTextView.text = "(${mediaItems.size}首)"

                    // 更新适配器数据
                    adapter.updateMediaItems(mediaItems)

                    // 显示RecyclerView和操作按钮，隐藏空提示
                    recyclerView.visibility = View.VISIBLE
                    clearButton?.visibility = View.VISIBLE
                    shuffleButton?.visibility = View.VISIBLE
                    emptyView.visibility = View.GONE
                } else {
                    // 显示空提示，隐藏RecyclerView和操作按钮
                    recyclerView.visibility = View.GONE
                    clearButton?.visibility = View.GONE
                    shuffleButton?.visibility = View.GONE
                    emptyView.visibility = View.VISIBLE
                }
            }
        }

        // 设置清空播放列表按钮
        clearButton?.setOnClickListener {
            showClearPlaylistDialog {
                viewModel.clearPlaylist()
                dialog.dismiss()
            }
        }

        // 设置随机播放按钮
        shuffleButton?.setOnClickListener {
            viewModel.shufflePlaylist()
            showSnackbar("播放列表已随机排序")
        }

        // 获取关闭按钮
        val closeButton = dialogView.findViewById<ImageButton>(R.id.button_playlist_close)

        // 设置关闭按钮点击事件
        closeButton.setOnClickListener {
            dialog.dismiss()
        }

        // 显示对话框
        dialog.show()
    }

    /**
     * 显示删除歌曲确认对话框
     */
    private fun showDeleteSongDialog(mediaItem: androidx.media3.common.MediaItem, position: Int, onConfirm: () -> Unit) {
        AlertDialog.Builder(requireContext())
            .setTitle("删除歌曲")
            .setMessage("确定要从播放列表中删除「${mediaItem.mediaMetadata.title}」吗？")
            .setPositiveButton("删除") { _, _ ->
                viewModel.removeFromPlaylist(position)
                onConfirm()
                showSnackbar("已从播放列表中删除")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示清空播放列表确认对话框
     */
    private fun showClearPlaylistDialog(onConfirm: () -> Unit) {
        AlertDialog.Builder(requireContext())
            .setTitle("清空播放列表")
            .setMessage("确定要清空整个播放列表吗？此操作不可撤销。")
            .setPositiveButton("清空") { _, _ ->
                onConfirm()
                showSnackbar("播放列表已清空")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示Snackbar提示
     */
    private fun showSnackbar(message: String) {
        try {
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.theme_primary))
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.text_light))
                .show()
        } catch (e: Exception) {
            Log.e(TAG, "显示Snackbar失败", e)
        }
    }

    /**
     * 切换到评论页面
     * 使用ViewPager2内嵌显示，提供更流畅的用户体验
     */
    private fun switchToCommentPage() {
        // 检查当前是否有歌曲在播放
        if (viewModel.currentSong.value == null) {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        val startTime = System.currentTimeMillis()

        // 平滑切换到评论页面
        binding.viewPagerPlayer.setCurrentItem(
            PlayerPagerAdapter.PAGE_COMMENT,
            true // 启用平滑动画
        )

        // 监控切换性能
        val responseTime = System.currentTimeMillis() - startTime
        Log.d(TAG, "切换到评论页面，响应时间: ${responseTime}ms")

        if (responseTime > 200) {
            Log.w(TAG, "页面切换响应时间警告: ${responseTime}ms > 200ms")
        }
    }

    /**
     * 切换到播放列表页面
     * 使用ViewPager2内嵌显示，提供更流畅的用户体验
     */
    private fun switchToPlaylistPage() {
        val startTime = System.currentTimeMillis()

        // 平滑切换到播放列表页面
        binding.viewPagerPlayer.setCurrentItem(
            PlayerPagerAdapter.PAGE_PLAYLIST,
            true // 启用平滑动画
        )

        // 监控切换性能
        val responseTime = System.currentTimeMillis() - startTime
        Log.d(TAG, "切换到播放列表页面，响应时间: ${responseTime}ms")

        if (responseTime > 200) {
            Log.w(TAG, "页面切换响应时间警告: ${responseTime}ms > 200ms")
        }
    }

    /**
     * 返回到歌词页面
     * 从评论或播放列表页面返回歌词页面
     */
    fun switchToLyricPage() {
        val startTime = System.currentTimeMillis()

        // 平滑切换到歌词页面
        binding.viewPagerPlayer.setCurrentItem(
            PlayerPagerAdapter.PAGE_LYRIC,
            true // 启用平滑动画
        )

        // 监控切换性能
        val responseTime = System.currentTimeMillis() - startTime
        Log.d(TAG, "返回到歌词页面，响应时间: ${responseTime}ms")
    }

    /**
     * 显示心动模式
     * 使用Navigation导航到IntelligenceFragment
     */
    private fun showHeartModeDialog() {
        // 检查当前是否有歌曲在播放
        if (viewModel.currentSong.value == null) {
            Toast.makeText(requireContext(), "请先播放一首歌曲", Toast.LENGTH_SHORT).show()
            return
        }

        // 获取当前歌曲ID
        val songId = viewModel.currentSong.value?.id ?: return

        // 使用Navigation导航到IntelligenceFragment
        val action = PlayerFragmentDirections.actionPlayerFragmentToIntelligenceFragment(
            songId = songId,
            playlistId = -1L,
        )
        findNavController().navigate(action)
    }

    /**
     * 设置对话框动画效果
     */
    private fun setupDialogAnimation(dialog: BottomSheetDialog) {
        // 获取对话框内容视图
        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let {
            // 设置初始状态
            it.alpha = 0f
            it.scaleX = 0.8f
            it.scaleY = 0.8f

            // 创建动画
            it.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(250)
                .setInterpolator(android.view.animation.OvershootInterpolator(0.8f))
                .start()

            // 设置对话框消失时的动画
            dialog.setOnDismissListener {
                bottomSheet.animate()
                    .alpha(0f)
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(200)
                    .start()
            }
        }
    }

    /**
     * 显示错误提示
     */
    private fun showErrorSnackbar(message: String) {
        try {
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.theme_error))
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.text_light))
                .show()
        } catch (e: Exception) {
            Log.e(TAG, "显示错误提示失败", e)
        }
    }

    /**
     * 启动跑马灯效果（如果文本过长）
     */
    private fun startMarqueeIfNeeded(textView: TextView) {
        try {
            // 延迟执行，确保TextView已经完成布局
            textView.post {
                // 测量文本宽度
                val paint = textView.paint
                val textWidth = paint.measureText(textView.text.toString())
                val viewWidth = textView.width - textView.paddingLeft - textView.paddingRight

                // 如果文本宽度超过视图宽度，启动跑马灯
                if (textWidth > viewWidth && viewWidth > 0) {
                    textView.isSelected = true // 启动跑马灯
                    Log.d(TAG, "启动跑马灯: ${textView.text}, 文本宽度: $textWidth, 视图宽度: $viewWidth")
                } else {
                    textView.isSelected = false // 停止跑马灯
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动跑马灯失败", e)
        }
    }

    /**
     * 显示错误弹窗
     */
    private fun showErrorDialog(title: String, message: String) {
        try {
            if (isAdded && !requireActivity().isFinishing) {
                AlertDialog.Builder(requireContext())
                    .setTitle(title)
                    .setMessage(message)
                    .setPositiveButton("确定") { dialog, _ ->
                        dialog.dismiss()
                    }
                    .setCancelable(true)
                    .show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示错误弹窗失败", e)
        }
    }

    /**
     * 优化触摸反馈
     */
    private fun addTouchFeedback(view: View) {
        view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时缩放
                    v.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .setDuration(100)
                        .start()

                    // 触觉反馈
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        @Suppress("DEPRECATION")
                        val vibrator = requireActivity().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                        vibrator.vibrate(VibrationEffect.createOneShot(10, VibrationEffect.DEFAULT_AMPLITUDE))
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 释放时恢复
                    v.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }
            }
            false
        }
    }

    /**
     * 设置搜索功能 - 重构版本
     */
    private fun setupSearchFunction() {
        // 初始化搜索建议适配器 - 支持历史记录和建议
        searchSuggestionsAdapter = SearchSuggestionsAdapter(
            onItemClick = { searchItem ->
                // 处理搜索项点击
                val text = when (searchItem) {
                    is com.example.aimusicplayer.data.model.SearchItem.SuggestionItem -> searchItem.text
                    is com.example.aimusicplayer.data.model.SearchItem.HistoryItem -> searchItem.text
                }
                Log.d(TAG, "点击搜索项: $text")
                binding.searchEditText.setText(text)
                binding.searchEditText.setSelection(text.length)
                viewModel.onSearchItemClick(text)
                hideKeyboard()
            },
            onHistoryLongClick = { historyItem ->
                // 处理历史记录长按删除
                showDeleteHistoryDialog(historyItem)
            },
        )

        // 初始化搜索结果适配器
        searchResultsAdapter = SearchResultsAdapter { song ->
            // 点击搜索结果，播放歌曲
            Log.d(TAG, "点击搜索结果: ${song.name} - ${song.getArtistNames()}, ID: ${song.id}")
            try {
                viewModel.playSearchResult(song)
                hideSearchResults()
                hideKeyboard()
                // 收缩搜索框
                collapseSearchBox()
            } catch (e: Exception) {
                Log.e(TAG, "播放搜索结果失败", e)
                showErrorSnackbar("播放失败，请重试")
            }
        }

        // 设置RecyclerView
        binding.searchSuggestionsRecycler.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = searchSuggestionsAdapter
        }

        binding.searchResultsRecycler.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = searchResultsAdapter
        }

        // 搜索框文本变化监听 - 添加防抖动处理
        binding.searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val query = s?.toString()?.trim() ?: ""

                // 取消之前的防抖动任务
                searchDebounceJob?.cancel()

                if (query.isNotEmpty()) {
                    // 使用防抖动处理，300ms后执行搜索建议
                    searchDebounceJob = lifecycleScope.launch {
                        delay(300) // 防抖动延迟
                        if (isActive) { // 确保协程仍然活跃
                            viewModel.getSearchSuggestions(query)
                            hideSearchResults() // 隐藏之前的搜索结果
                        }
                    }
                } else {
                    // 立即清空建议和结果
                    viewModel.clearSearchResults()
                    hideSearchSuggestions()
                    hideSearchResults()
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        // 搜索框焦点变化监听 - 获得焦点时显示历史记录
        binding.searchEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                expandSearchBox()
                // 获得焦点时立即显示搜索历史
                viewModel.getSearchSuggestions("")
            } else {
                if (binding.searchEditText.text.toString().trim().isEmpty()) {
                    collapseSearchBox()
                }
            }
        }

        // 搜索框按键监听（Enter键搜索）
        binding.searchEditText.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)
            ) {
                val query = binding.searchEditText.text.toString().trim()
                if (query.isNotEmpty()) {
                    performSearch(query)
                    hideKeyboard()
                }
                true
            } else {
                false
            }
        }

        // 搜索按钮点击监听
        binding.searchButton.setOnClickListener {
            val query = binding.searchEditText.text.toString().trim()
            if (query.isNotEmpty()) {
                performSearch(query)
                hideKeyboard()
            } else {
                // 如果搜索框为空，聚焦到搜索框并展开
                expandSearchBox()
                binding.searchEditText.requestFocus()
                showKeyboard()
            }
        }

        // 搜索框点击监听 - 点击后展开
        binding.searchEditText.setOnClickListener {
            expandSearchBox()
        }

        // 心动模式按钮点击监听
        binding.heartModeButton.setOnClickListener {
            addButtonClickEffect(it)
            // 检查登录状态
            if (!checkLoginStatusForFeature("心动模式")) {
                return@setOnClickListener
            }
            toggleHeartMode()
        }

        // 评论按钮点击监听 - 切换到评论页面
        binding.buttonPlayerComment.setOnClickListener {
            addButtonClickEffect(it)
            switchToCommentPage()
        }

        // 设置点击外部区域收缩搜索框
        setupOutsideClickListener()
    }

    /**
     * 展开搜索框 - 长度变为两倍
     */
    private fun expandSearchBox() {
        if (isSearchExpanded) return

        Log.d(TAG, "展开搜索框")
        isSearchExpanded = true

        val searchEditText = binding.searchEditText
        val currentWidth = 140.dpToPx() // 当前宽度140dp
        val expandedWidth = 280.dpToPx() // 展开后宽度280dp（两倍）

        // 使用硬件加速的动画
        searchEditText.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        val animator = ValueAnimator.ofInt(currentWidth, expandedWidth)
        animator.duration = 300
        animator.interpolator = DecelerateInterpolator(2f) // 更流畅的减速效果
        animator.addUpdateListener { animation ->
            val animatedValue = animation.animatedValue as Int
            val layoutParams = searchEditText.layoutParams
            layoutParams.width = animatedValue
            searchEditText.layoutParams = layoutParams
        }
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 动画结束后恢复默认渲染模式
                searchEditText.setLayerType(View.LAYER_TYPE_NONE, null)
                Log.d(TAG, "搜索框展开完成")
            }
        })
        animator.start()

        // 同时调整搜索结果和建议框的宽度
        adjustSearchContainersWidth(expandedWidth)
    }

    /**
     * 收缩搜索框 - 恢复到原始大小
     */
    private fun collapseSearchBox() {
        if (!isSearchExpanded) return

        Log.d(TAG, "收缩搜索框")
        isSearchExpanded = false

        val searchEditText = binding.searchEditText
        val currentWidth = searchEditText.width
        val collapsedWidth = 140.dpToPx() // 恢复到140dp

        // 使用硬件加速的动画
        searchEditText.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        val animator = ValueAnimator.ofInt(currentWidth, collapsedWidth)
        animator.duration = 300
        animator.interpolator = AccelerateInterpolator(2f) // 更流畅的加速效果
        animator.addUpdateListener { animation ->
            val animatedValue = animation.animatedValue as Int
            val layoutParams = searchEditText.layoutParams
            layoutParams.width = animatedValue
            searchEditText.layoutParams = layoutParams
        }
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 动画结束后恢复默认渲染模式
                searchEditText.setLayerType(View.LAYER_TYPE_NONE, null)
                Log.d(TAG, "搜索框收缩完成")
            }
        })
        animator.start()

        // 同时调整搜索结果和建议框的宽度
        adjustSearchContainersWidth(collapsedWidth)
    }

    /**
     * 调整搜索容器宽度 - 与搜索框保持一致
     */
    private fun adjustSearchContainersWidth(width: Int) {
        try {
            // 调整搜索建议容器宽度
            val suggestionsLayoutParams = binding.searchSuggestionsRecycler.layoutParams
            suggestionsLayoutParams.width = width
            binding.searchSuggestionsRecycler.layoutParams = suggestionsLayoutParams

            // 调整搜索结果容器宽度
            val resultsLayoutParams = binding.searchResultsRecycler.layoutParams
            resultsLayoutParams.width = width
            binding.searchResultsRecycler.layoutParams = resultsLayoutParams

            Log.d(TAG, "搜索容器宽度已调整为: ${width}px")
        } catch (e: Exception) {
            Log.e(TAG, "调整搜索容器宽度失败", e)
        }
    }

    /**
     * 设置点击外部区域收缩搜索框
     */
    private fun setupOutsideClickListener() {
        // 设置根布局点击监听，点击外部区域收缩搜索框
        binding.root.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN && isSearchExpanded) {
                val searchEditText = binding.searchEditText
                val location = IntArray(2)
                searchEditText.getLocationOnScreen(location)

                val x = event.rawX
                val y = event.rawY

                // 检查点击是否在搜索框外部
                if (x < location[0] || x > location[0] + searchEditText.width ||
                    y < location[1] || y > location[1] + searchEditText.height
                ) {
                    // 如果搜索框为空，收缩搜索框
                    if (searchEditText.text.toString().trim().isEmpty()) {
                        collapseSearchBox()
                        hideKeyboard()
                    }
                }
            }
            false
        }
    }

    /**
     * 执行搜索 - 添加性能监控
     */
    private fun performSearch(query: String) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "执行搜索: $query")

        // 隐藏搜索建议，显示搜索结果
        hideSearchSuggestions()

        // 显示搜索结果容器
        binding.searchResultsRecycler.visibility = View.VISIBLE
        binding.viewPagerPlayer.visibility = View.GONE

        // 执行搜索请求
        viewModel.searchSongs(query)

        // 监控搜索性能
        lifecycleScope.launch {
            viewModel.searchResults.collectLatest { results ->
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                Log.d(TAG, "搜索完成: 关键词=$query, 结果=${results.size}首, 耗时=${duration}ms")

                // 如果搜索时间超过3秒，记录警告
                if (duration > 3000) {
                    Log.w(TAG, "搜索性能警告: 搜索耗时${duration}ms，超过3秒阈值")
                }
            }
        }
    }

    /**
     * dp转px的扩展函数
     */
    private fun Int.dpToPx(): Int {
        return (this * resources.displayMetrics.density).toInt()
    }

    /**
     * 隐藏搜索建议
     */
    private fun hideSearchSuggestions() {
        binding.searchSuggestionsRecycler.visibility = View.GONE
    }

    /**
     * 隐藏搜索结果
     */
    private fun hideSearchResults() {
        binding.searchResultsRecycler.visibility = View.GONE
        binding.viewPagerPlayer.visibility = View.VISIBLE
    }

    /**
     * 显示键盘
     */
    private fun showKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(binding.searchEditText, InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * 隐藏键盘
     */
    private fun hideKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.searchEditText.windowToken, 0)
        binding.searchEditText.clearFocus()
    }

    /**
     * 处理网络状态变化
     */
    private fun handleNetworkStateChange(networkState: NetworkStateManager.NetworkState) {
        Log.d(TAG, "网络状态变化: $networkState")

        // 根据网络状态显示不同的提示
        when {
            !networkState.isConnected -> {
                showErrorSnackbar("网络连接已断开，部分功能可能无法使用")
            }
            networkState.isCellular && networkState.isMetered -> {
                // 移动网络且计费，提示用户
                Log.d(TAG, "当前使用移动网络，已优化数据使用")
            }
            networkState.isWifi -> {
                Log.d(TAG, "WiFi连接良好，享受高质量音乐体验")
            }
        }
    }

    /**
     * 处理请求策略变化
     */
    private fun handleRequestStrategyChange(strategy: NetworkStateManager.RequestStrategy) {
        Log.d(TAG, "请求策略更新: $strategy")

        // 根据策略调整图片加载质量
        if (!strategy.enableHighQualityImages) {
            Log.d(TAG, "网络较慢，已切换到低质量图片模式")
        }

        // 根据策略调整预加载
        if (!strategy.enablePreloading) {
            Log.d(TAG, "网络受限，已禁用预加载功能")
        }
    }

    /**
     * 显示删除历史记录对话框
     */
    private fun showDeleteHistoryDialog(historyItem: com.example.aimusicplayer.data.model.SearchItem.HistoryItem) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("删除搜索历史")
            .setMessage("确定要删除搜索记录 \"${historyItem.text}\" 吗？")
            .setPositiveButton("删除") { _, _ ->
                viewModel.deleteSearchHistory()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()

        try {
            // 取消网络状态监听
            networkStateManager.unregisterNetworkCallback()

            // 取消颜色动画
            backgroundColorAnimator?.cancel()
            backgroundColorAnimator = null

            // 取消专辑旋转动画
            albumRotationAnimator?.cancel()
            albumRotationAnimator = null

            // 取消歌曲切换动画
            songTransitionAnimator?.cancel()
            songTransitionAnimator = null

            // 清理缓存
            lifecycleScope.launch {
                try {
                    // AlbumArtCache会自动管理缓存，无需手动清理
                    Log.d(TAG, "缓存清理已由AlbumArtCache自动管理")
                } catch (e: Exception) {
                    Log.e(TAG, "清理缓存失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "销毁视图时发生错误", e)
        } finally {
            _binding = null
        }
    }
}
